#!/usr/bin/env python3
"""检查TestHelper.py中缺少类型注解的方法"""

import re
import ast

def find_methods_without_type_annotations(file_path):
    """查找缺少类型注解的方法"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找所有方法定义
    method_pattern = r'^\s*def\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\([^)]*\):'
    methods = re.findall(method_pattern, content, re.MULTILINE)
    
    # 查找有类型注解的方法
    typed_method_pattern = r'^\s*def\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\([^)]*\)\s*->\s*[^:]+:'
    typed_methods = re.findall(typed_method_pattern, content, re.MULTILINE)
    
    # 找出缺少类型注解的方法
    untyped_methods = []
    for method in methods:
        if method not in typed_methods and not method.startswith('_'):  # 跳过私有方法
            untyped_methods.append(method)
    
    return untyped_methods

def find_method_definitions(file_path):
    """查找所有方法定义的详细信息"""
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    method_info = []
    for i, line in enumerate(lines, 1):
        # 匹配方法定义
        match = re.match(r'^(\s*)def\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\(([^)]*)\)(\s*->\s*[^:]+)?:', line)
        if match:
            indent, method_name, params, return_type = match.groups()
            method_info.append({
                'line_number': i,
                'method_name': method_name,
                'parameters': params,
                'has_return_type': return_type is not None,
                'full_line': line.strip()
            })
    
    return method_info

if __name__ == "__main__":
    file_path = "TestHelper.py"
    
    print("=== 检查TestHelper.py中的类型注解 ===\n")
    
    # 查找所有方法
    method_info = find_method_definitions(file_path)
    
    print(f"总共找到 {len(method_info)} 个方法\n")
    
    # 分类统计
    public_methods = [m for m in method_info if not m['method_name'].startswith('_')]
    private_methods = [m for m in method_info if m['method_name'].startswith('_')]
    
    typed_public = [m for m in public_methods if m['has_return_type']]
    untyped_public = [m for m in public_methods if not m['has_return_type']]
    
    print(f"公共方法: {len(public_methods)} 个")
    print(f"  - 有类型注解: {len(typed_public)} 个")
    print(f"  - 缺少类型注解: {len(untyped_public)} 个")
    print(f"私有方法: {len(private_methods)} 个")
    
    if untyped_public:
        print(f"\n=== 缺少类型注解的公共方法 ({len(untyped_public)} 个) ===")
        for method in untyped_public:
            print(f"行 {method['line_number']:4d}: {method['method_name']}")
            print(f"         {method['full_line']}")
            print()
    
    print("=== 检查完成 ===")
