"""CIM8光模块校准和测试的测试辅助模块。

本模块为CIM8光模块提供全面的测试和校准功能，包括TX/RX校准、
功率监控和各种光学参数测量。

@Time        : 2025/5/23 15:06
<AUTHOR> 53211
@File        : TestHelper.py
@Project     : SIM8
@Description : CIM8光模块测试辅助工具，包含校准功能
"""
import math
import os
import subprocess
import sys
import time
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union

import numpy as np
import openpyxl
import pandas as pd
from openpyxl.reader.excel import load_workbook
from openpyxl.styles import PatternFill
from openpyxl.utils import get_column_letter
from scipy.special import erfcinv

from Processes.CIM8.data_save import find_extrema
from TestHelperBase import TestHelperBase
from device_interface.cim8 import Ax1200PcieDefs
from device_interface.cim8.Cim8Functions import Cim8Functions
from device_interface.cim8.Functions.math_function import linear_fit, signed_num, float2hex
from device_interface.cim8.cim8_test_api import Cim8Base
from device_interface.opm.OPM_OLP85 import OPM_OLP85
from device_interface.osa.OSA_AQ6370 import OSA_AQ6370
from device_interface.osw.OSW_OLP_Board_UDP import OSW_OLP_Board_UDP
from device_interface.sop.SOP import SOP
from device_interface.utils.mylog import logger, frame_log_file_name, module_log_file_name
from device_interface.voa.VOA_Board import VOA_Board
from pcs.PCS import pcs

# 模块级常量定义
CALIBRATION_TIME_LIMIT_SECONDS = 60  # 校准时间限制（秒）
DEMOD_LOCK_MIN_TIME_SECONDS = 60  # 解调器锁定最小时间（秒）
DEMOD_LOCK_WAIT_BUFFER_SECONDS = 65  # 解调器锁定等待缓冲时间（秒）
RX90_CALIBRATION_SLEEP_SECONDS = 5  # RX90校准休眠时间（秒）
RX90_STATUS_CHECK_INTERVAL_SECONDS = 5  # RX90状态检查间隔（秒）
RX90_VOLTAGE_LOWER_LIMIT = 2.2  # RX90电压下限
RX90_VOLTAGE_UPPER_LIMIT = 5.2  # RX90电压上限
RX90_VOLTAGE_ADJUSTMENT_OFFSET = 6.5  # RX90电压调整偏移量
RESET_TIME_THRESHOLD_SECONDS = 30  # 复位时间阈值（秒）
RESET_TIME_BUFFER_SECONDS = 35  # 复位时间缓冲（秒）
EEPROM_HEADER_VALUE = 0xCAFEBABE  # EEPROM头部值
NULL_VOLTAGE_OFFSET_RANGE = [round(3.1 + 0.05 * i, 2) for i in range(0, 17)]  # 零电压偏移范围
LIGHT_SPEED_M_PER_S = 299792458  # 光速（米/秒）
TX_POWER_THRESHOLD_DBM = -18  # 发射功率阈值（dBm）
TX_POWER_UPPER_THRESHOLD_DBM = -15  # 发射功率上阈值（dBm）
TX_POWER_MINIMUM_THRESHOLD_DBM = -20  # 发射功率最小阈值（dBm）
VOA_STEP_ADJUSTMENT = 0.5  # VOA步进调整值
TOF_NULL_OFFSET_MA2 = 3.25  # TOF零点偏移量（mA²）
PI_HALF = 1.57  # π/2
PI = 3.14  # π

# 全局初始化标志
init_flag = False


class TestHelper(TestHelperBase):
    """CIM8光模块校准和测试的测试辅助类。

    本类为CIM8光模块的测试和校准提供全面的功能，包括功率测量、
    频谱分析和各种校准程序。

    Attributes:
        INPUT_BOXES: 模块序列号输入框配置。
        DEPUTY_BUTTONS: UI按钮状态配置。
    """

    # 用于UI配置的类级常量
    INPUT_BOXES = [
        {
            "module_sn_1": "module_sn_1",
            "module_sn_2": "module_sn_2",
            "module_sn_3": "module_sn_3",
            "module_sn_4": "module_sn_4"
        },
        {
            "module_mn_1": "module_mn_1",
            "module_mn_2": "module_mn_2",
            "module_mn_3": "module_mn_3",
            "module_mn_4": "module_mn_4"
        }
    ]

    DEPUTY_BUTTONS = [
        {}, {},
        {'报错按钮变红色': {4: 0}},
        {'正常按钮保持绿色': {94: 0}},
        {}, {}
    ]

    def __init__(self) -> None:
        """初始化TestHelper实例。

        将所有设备接口和配置参数设置为None。
        实际初始化在init_params_in_end_test()中进行。
        """
        super().__init__()

        # 测试配置
        self.test_mode: Optional[str] = None

        # 光学测量设备
        self.opm: Optional[OPM_OLP85] = None
        self.osa: Optional[OSA_AQ6370] = None
        self.sop: Optional[SOP] = None
        self.sop_out_channel: Optional[int] = None
        self.sop_in_channel: Optional[int] = None

        # 可变光衰减器
        self.voa_ase: Optional[VOA_Board] = None
        self.voa_rx: Optional[VOA_Board] = None

        # 光开关
        self.osw_tr: Optional[OSW_OLP_Board_UDP] = None
        self.osw_tr_tx_pow: Optional[int] = None
        self.osw_tr_rx_pow: Optional[int] = None
        self.osw_ase: Optional[OSW_OLP_Board_UDP] = None
        self.osw_ase_on: Optional[int] = None
        self.osw_ase_off: Optional[int] = None
        self.osw_sop: Optional[OSW_OLP_Board_UDP] = None
        self.osw_sop_on: Optional[int] = None
        self.osw_sop_off: Optional[int] = None

        # CIM8模块接口
        self.cim8: Optional[Cim8Base] = None
        self.cim8_func: Optional[Cim8Functions] = None

        # 校准数据
        self.rx_offset: Optional[Dict[float, float]] = None
        self.tx_offset: Optional[Dict[float, float]] = None

        # 文件路径
        self.pre_cal_file_path: Optional[str] = None
        self.cal_file_path: Optional[str] = None
        self.cal_result_path: Optional[str] = None
        self.exe_path: Optional[str] = None

        # 校准用零电压值
        self.null_voltage_values: List[float] = NULL_VOLTAGE_OFFSET_RANGE
        self.result_file_list: Optional[List[str]] = None

    def init_params_in_end_test(self) -> None:
        """初始化参数、工作站和设备。

        此方法执行所有测试参数、设备连接和文件路径的一次性初始化。
        使用全局标志确保初始化只进行一次。
        """

        def _init_params_once() -> None:
            """执行一次性参数初始化的内部函数。"""
            # 初始化基础参数
            self.init_Params()

            # 初始化工作站信息
            self._init_workstation_info()

            # 初始化设备连接
            self._init_device_connections()

            # 初始化CIM8模块
            self._init_cim8_module()

            # 初始化文件路径和变量
            self._init_file_paths_and_variables()

        global init_flag
        if not init_flag:
            _init_params_once()
            init_flag = True

    def _init_workstation_info(self) -> None:
        """初始化工作站和登录信息。"""
        self.employee_no = self.getParamByName('employee_no')
        self.raw_password = self.getParamByName('raw_password')
        self.work_site = self.getParamByName('work_site')
        self.process = self.getParamByName('process')
        self.mn = self.getParamByName('mn')

        logger.info(
            f"工作站信息 - 员工号: {self.employee_no}, "
            f"工作站: {self.work_site}, 工序: {self.process}, 型号: {self.mn}"
        )

        # 如果提供了凭据则登录工作站
        if all([self.employee_no, self.raw_password, self.work_site]):
            self.pcs_obj = pcs
            self.pcs_obj.login(self.employee_no, self.raw_password, self.work_site)

    def _init_device_connections(self) -> None:
        """初始化所有光学设备连接和配置。"""
        # 光学测量设备
        self.opm = self.getParamByName('opm')
        self.osa = self.getParamByName('osa')
        self.sop = self.getParamByName('sop')
        self.sop_out_channel = int(self.getParamByName('sop_out_channel'))
        self.sop_in_channel = int(self.getParamByName('sop_in_channel'))

        # 可变光衰减器
        self.voa_ase = self.getParamByName('voa_ase')
        self.voa_rx = self.getParamByName('voa_rx')

        # 光开关
        self.osw_tr = self.getParamByName('osw_tr')
        self.osw_tr_tx_pow = self.getParamByName('osw_tr_tx_pow')
        self.osw_tr_rx_pow = self.getParamByName('osw_tr_rx_pow')
        self.osw_ase = self.getParamByName('osw_ase')
        self.osw_ase_on = self.getParamByName('osw_ase_on')
        self.osw_ase_off = self.getParamByName('osw_ase_off')
        self.osw_sop = self.getParamByName('osw_sop')
        self.osw_sop_on = int(self.getParamByName('osw_sop_on'))
        self.osw_sop_off = int(self.getParamByName('osw_sop_off'))

        # 将光开关初始化到默认位置
        self._configure_optical_switches()

        # 将VOA初始化到最大衰减
        self.voa_rx.set_attr(60)

    def _configure_optical_switches(self) -> None:
        """配置光开关到默认位置。"""
        self.osw_tr.switch_channel(self.osw_tr_rx_pow)
        self.osw_ase.switch_channel(self.osw_ase_off)
        self.osw_sop.switch_channel(self.osw_sop_off)

    def _init_cim8_module(self) -> None:
        """初始化CIM8模块并获取模块信息。"""
        # 初始化CIM8模块接口
        self.cim8 = self.getParamByName('cim8')
        self.cim8_func = self.getParamByName('cim8_func')

        # 获取模块序列号和其他标识符
        self.cim8_sn = self.cim8.get_SN()
        self.sn = self.cim8_sn  # 保持向后兼容性
        self.mn = self.getParamByName('mn')
        self.sn = self.getParamByName('sn')  # 如果参数可用则覆盖
        self.pn = self.getParamByName('pn')
        self.ispass = self.getParamByName('ispass')

    def _init_file_paths_and_variables(self) -> None:
        """初始化文件路径、校准数据和其他变量。"""
        # 初始化校准偏移数据
        self.tx_offset = self.getParamByName('tx_offset')
        self.rx_offset = self.getParamByName('rx_offset')

        # 生成带时间戳的校准文件路径
        self._setup_calibration_file_paths()

        # 设置结果目录
        self._setup_result_directory()

        # 初始化结果文件列表
        self._setup_result_file_list()

        # 获取测试模式和可执行文件路径
        self.test_mode = self.getParamByName('test_mode')
        self.exe_path = self.getParamByName('exe_path')

    def _setup_calibration_file_paths(self) -> None:
        """设置带时间戳的校准文件路径。"""
        self.pre_cal_file_path_prefix = self.getParamByName('pre_cal_file_path_prefix')
        self.cal_file_path_prefix = self.getParamByName('cal_file_path_prefix')
        current_time_str = time.strftime("%m%d%H%M", time.localtime())

        self.pre_cal_file_path = (
            f'{self.pre_cal_file_path_prefix}{self.cim8_sn}_{current_time_str}.xlsx'
        )
        self.cal_file_path = (
            f'{self.cal_file_path_prefix}{self.cim8_sn}_{current_time_str}.xlsx'
        )

    def _setup_result_directory(self) -> None:
        """设置并创建结果目录（如果不存在）。"""
        self.cal_result_path = self.getParamByName('cal_result_path_prefix') + self.cim8.SN
        cal_result_path = Path(self.cal_result_path)

        if not cal_result_path.exists():
            try:
                cal_result_path.mkdir(parents=True, exist_ok=True)
                logger.info(f"已创建结果目录: '{cal_result_path}'")
            except OSError as e:
                logger.error(f"创建结果目录失败: {e}")
        else:
            logger.info(f"结果目录已存在: '{cal_result_path}'")

    def _setup_result_file_list(self) -> None:
        """设置要上传的结果文件列表。"""
        self.result_file_list = list(self.getParamByName('result_file_list'))

        # 将日志文件添加到结果文件列表
        log_path = os.path.join(os.path.dirname(sys.argv[0]), 'log')
        frame_log_file_path = os.path.join(log_path, frame_log_file_name)
        module_log_file_path = os.path.join(log_path, module_log_file_name)

        self.result_file_list.extend([
            frame_log_file_path,
            module_log_file_path,
            self.pre_cal_file_path,
            self.cal_file_path
        ])

    def get_result_file_list(self) -> List[str]:
        """获取要上传的测试数据文件完整列表。

        Returns:
            需要上传的文件路径列表。
        """
        return self.result_file_list

    def append_result_file_list(self, result_path: str) -> None:
        """将结果文件路径添加到上传列表。

        Args:
            result_path: 要添加的结果文件路径。
        """
        self.result_file_list.append(result_path)

    @staticmethod
    def create_object(object_value) -> object:
        """记录并返回输入参数作为变量。

        Args:
            object_value: 要记录和返回的值。

        Returns:
            传入的同一个对象。
        """
        logger.info(object_value)
        return object_value

    def set_test_mode(self, test_mode: str) -> None:
        """设置当前测试模式。

        Args:
            test_mode: 要设置的测试模式名称。
        """
        self.test_mode = test_mode

    def write_to_sheet(self, sheet_name: str, data_name: str, data: Union[str, float, int],
                      judge_flag: int = 0, limit: str = '-') -> None:
        """将数值写入指定位置

        Args:
            sheet_name: sheet名
            data_name: 数值名
            data: 实际值
            judge_flag: 是否需要判断pass/fail
            limit: 判断范围,列表格式[min,max]
        """
        if self.test_mode == "pre_cal":
            file_path = self.pre_cal_file_path
        elif self.test_mode == "cal":
            file_path = self.cal_file_path
        else:
            logger.error("当前测试模式不正确，无法写入")
            return
        try:
            wb = openpyxl.load_workbook(file_path)
        except FileNotFoundError:
            wb = openpyxl.Workbook()
        if sheet_name not in wb.sheetnames:
            wb.create_sheet(sheet_name)
        sheet = wb[sheet_name]
        header_empty = True
        for cell in sheet[1]:
            if cell.value:
                header_empty = False
                break
        if header_empty:
            headers = ['测试内容', '测试值', '是否判断', '范围', '是否合格']
            for col_index, header in enumerate(headers, start=1):
                col_letter = get_column_letter(col_index)
                sheet[f'{col_letter}1'] = header
        next_row = sheet.max_row + 1
        if judge_flag:  # 需要判断范围的
            sheet.cell(row=next_row, column=1, value=data_name)
            sheet.cell(row=next_row, column=2, value=data)
            sheet.cell(row=next_row, column=3, value=judge_flag)
            sheet.cell(row=next_row, column=4, value=str(limit))
            if limit[0] <= data <= limit[1]:
                cell = sheet.cell(row=next_row, column=5, value='PASS')
                fill = PatternFill(patternType='solid', fgColor='90EE90')
                cell.fill = fill
                logger.info(f'{data_name} 合格')
                wb.save(file_path)

                wb.close()
                logger.info(f"数据已保存到 {file_path} 的 {sheet_name} 页，第 {next_row} 行。")
                return True
            else:
                cell = sheet.cell(row=next_row, column=5, value='FAIL')
                fill = PatternFill(patternType='solid', fgColor='FF4C4C')
                cell.fill = fill
                logger.warning(f'{data_name} 不合格')
                wb.save(file_path)
                wb.close()
                logger.info(f"数据已保存到 {file_path} 的 {sheet_name} 页，第 {next_row} 行。")
                return False
        else:
            sheet.cell(row=next_row, column=1, value=data_name)
            sheet.cell(row=next_row, column=2, value=data)
            sheet.cell(row=next_row, column=3, value=judge_flag)
            sheet.cell(row=next_row, column=4, value='-')
        wb.save(file_path)
        wb.close()
        logger.info(f"数据已保存到 {file_path} 的 {sheet_name} 页，第 {next_row} 行。")

    @staticmethod
    def clear_bits(num: int, bit_positions: List[int]) -> int:
        """清除寄存器值中的指定位。

        Args:
            num: 原始寄存器值。
            bit_positions: 要清除的位位置列表（从0开始索引）。

        Returns:
            清除指定位后的修改寄存器值。
        """
        mask = num
        for pos in bit_positions:
            mask &= ~(1 << pos)
        return mask

    def tx_rx_verify(self, opr: str, cmd: str, values: List, sleep_time: float) -> None:
        """验证TX/RX状态，如需要则执行RX90命令。

        Args:
            opr: 命令的操作类型。
            cmd: 要执行的命令。
            values: 命令的值列表。
            sleep_time: 命令执行后的休眠时间。
        """
        if not self.cim8.get_txrx_state():
            self.cim8.excecute_mcm_and_disp_result(opr, cmd, values)
            time.sleep(sleep_time)

    def check_trx_turnup(self) -> None:
        """检查收发器启动状态，如未就绪则记录日志。"""
        if not self.cim8.wait_for_trx_turnup():
            logger.info(
                '无论RX ADC输出启动状态或模块RX启动状态0x3F如何，'
                '始终强制RX90控制环路'
            )

    def write_mcm_sn_to_excel(self) -> bool:
        """写入mcm sn 到预校准的第一个sheet名

        Returns:
            操作是否成功
        """
        mcm_sn = self.cim8.handleDiagSetGetCmd('eget', 'MCM_MFG_SERIAL_NUM', [1]).split('\n')[0].strip()

        try:
            # 加载工作簿
            workbook = load_workbook(self.pre_cal_file_path)

            # 获取第一个工作表
            first_sheet = workbook.worksheets[0]
            print(first_sheet)
            # 修改工作表名称
            first_sheet.title = mcm_sn
            # 保存工作簿
            workbook.save(self.pre_cal_file_path)
            print(f"成功将sn写入第一个工作表重命名为 '{mcm_sn}'")
            return True

        except Exception as e:
            print(f"错误: 修改工作表名称时发生异常: {e}")
            return False

    def set_sop_in(self) -> None:
        """设置接入sop"""
        self.osw_sop.switch_channel(self.osw_sop_on)

        self.sop.acquire_osw()
        self.sop.switch_in_out(self.sop_in_channel, self.sop_out_channel)
        self.sop.release_osw()
        result = self.sop.acquire_sop()
        while "成功" not in result:
            logger.info("获取sop权限失败，等待5秒后重试！")
            time.sleep(5)
            result = self.sop.acquire_sop()

        self.sop.sop_tri_state(True)
        time.sleep(0.5)
        self.sop.sop_tri_rate(500)

    def set_sop_out(self) -> None:
        """设置断开sop"""
        self.osw_sop.switch_channel(self.osw_sop_off)
        self.sop.release_sop()

    @staticmethod
    def get_RXPD(RXPD_HIGH: float, RXPDXY_THRESHOLD: float, RXPD_LOW: float) -> float:
        """根据阈值选择RXPD值。

        Args:
            RXPD_HIGH: 高RXPD值
            RXPDXY_THRESHOLD: RXPD阈值
            RXPD_LOW: 低RXPD值

        Returns:
            选择的RXPD值
        """
        if RXPD_HIGH < RXPDXY_THRESHOLD:
            RXPD = RXPD_HIGH
        else:
            RXPD = RXPD_LOW
        return RXPD

    def get_rx_power(self, freq: float) -> float:
        """获取RX功率。

        Args:
            freq: 频率值

        Returns:
            校正后的RX功率值
        """
        power = self.opm.read_power_dbm() + self.rx_offset[freq]
        # power = self.opm.read_power_dbm()
        return power

    def check_RXPD_RFPD_D_FA(self, result1: bool, result2: bool, RXPD_LOSS_PCAL: float) -> None:
        """检查RXPD和RFPD的D_FA状态。

        Args:
            result1: 第一个结果状态
            result2: 第二个结果状态
            RXPD_LOSS_PCAL: RXPD损耗校准值
        """
        if result1 and result2:
            PIC_TEMP = self.cim8.get_pic_temp()
            FREQ = self.cim8.get_freq()
            RXPD_LOG = RXPD_LOSS_PCAL
            # update
            list0 = self.cim8.excecute_mcm_and_disp_result('eget', "PIC_RXPD_T0_COEFF", [1])
            self.cim8.excecute_mcm_and_disp_result('ESET', "PIC_RXPD_T0_COEFF", [1] + list0[:2] + [RXPD_LOG])
            list1 = self.cim8.excecute_mcm_and_disp_result('eget', "PIC_RXPD_T1_COEFF", [1])
            self.cim8.excecute_mcm_and_disp_result('ESET', "PIC_RXPD_T1_COEFF", [1] + list1[:2] + [RXPD_LOG])
            list2 = self.cim8.excecute_mcm_and_disp_result('eget', "PIC_RXPD_T2_COEFF", [1])
            self.cim8.excecute_mcm_and_disp_result('ESET', "PIC_RXPD_T2_COEFF", [1] + list2[:2] + [RXPD_LOG])
            list3 = self.cim8.excecute_mcm_and_disp_result('eget', "PIC_RXPD_T3_COEFF", [1])
            self.cim8.excecute_mcm_and_disp_result('ESET', "PIC_RXPD_T3_COEFF", [1] + list3[:2] + [RXPD_LOG])
            self.cim8.excecute_mcm_and_disp_result('ESET', "PIC_RXPD_RESP", [1, PIC_TEMP, 0, FREQ])
            self.cim8.excecute_mcm_and_disp_result('ESET', "PIC_RXPD_RFPD_TEMP_DELTA", [1, 0, 10.0, PIC_TEMP, 75.0])
            list4 = self.cim8.excecute_mcm_and_disp_result('eget', "PIC_LO_RFPD_RESP_REF", [1])
            self.cim8.excecute_mcm_and_disp_result('ESET', "PIC_LO_RFPD_RESP_REF", [1, PIC_TEMP, FREQ] + list4[-2:])
            self.cim8.excecute_mcm_and_disp_result('ESET', "PIC_EEPROM_HDR", [1, 0xCAFEBABE])

    def rx90_calibration(self) -> None:
        """执行RX90校准，包含适当的错误处理和验证。

        此方法执行完整的RX90校准序列，包括解调器锁定验证、
        校准执行和结果验证。
        """
        logger.info('RX90校准开始')

        # 验证解调器锁定时间
        if not self._verify_demod_lock_time():
            return

        # 开始校准过程
        if not self._start_rx90_calibration():
            return

        # 等待校准完成
        calibration_result = self._wait_for_rx90_completion()
        if calibration_result != 3:  # 3表示成功完成
            return

        # 处理和验证校准结果
        self._process_rx90_results()

    def _verify_demod_lock_time(self) -> bool:
        """验证解调器锁定时间，如有必要则等待。

        Returns:
            如果解调器锁定时间足够则返回True。
        """
        demod_time = self.cim8.get_time_DemodLock()
        logger.info(f'解调器锁定后的网络经过时间: {demod_time}秒')

        if demod_time < DEMOD_LOCK_MIN_TIME_SECONDS:
            wait_time = DEMOD_LOCK_WAIT_BUFFER_SECONDS - demod_time
            logger.info(f'等待{wait_time}秒以稳定解调器锁定')
            time.sleep(wait_time)
        return True

    def _start_rx90_calibration(self) -> bool:
        """开始RX90校准过程。

        Returns:
            如果校准成功开始则返回True。
        """
        rx_state = self.cim8.CheckDemodLock()
        if not rx_state:
            self.cim8.excecute_mcm_and_disp_result('set', 'PIC_FORCE_RX90_CNTL', [1, 1])

        self.cim8.excecute_mcm_and_disp_result("ESET", "PIC_STORE_RX90_SETTINGS", [1, 1])
        time.sleep(RX90_CALIBRATION_SLEEP_SECONDS)
        return True

    def _wait_for_rx90_completion(self) -> int:
        """等待RX90校准完成。

        Returns:
            校准状态码（3=成功，4=失败，其他=超时）。
        """
        start_time = time.time()

        while True:
            reply = self.cim8.rx_90_status()

            if reply == 1:
                logger.info('RX90校准进行中')
            elif reply == 2:
                logger.info('RX90正在收集校准数据')
            elif reply in [3, 4]:  # 完成或失败
                return reply

            # 检查超时
            if time.time() - start_time > CALIBRATION_TIME_LIMIT_SECONDS:
                logger.error('RX90校准超时 - 失败!')
                return -1

            time.sleep(RX90_STATUS_CHECK_INTERVAL_SECONDS)

    def _process_rx90_results(self) -> None:
        """处理和验证RX90校准结果。"""
        reply = self.cim8.handleDiagSetGetCmd("get", "PIC_STORE_RX90_SETTINGS", [1])
        line_str_list = reply.split('\n')

        try:
            rec_rx90_x = float(line_str_list[3])
            rec_rx90_y = float(line_str_list[4])
        except (IndexError, ValueError) as e:
            logger.error(f'解析RX90结果失败: {e}')
            return

        if self._validate_rx90_values(rec_rx90_x, rec_rx90_y):
            self._save_rx90_success()
        else:
            self._adjust_rx90_values(rec_rx90_x, rec_rx90_y)

    def _validate_rx90_values(self, rx90_x: float, rx90_y: float) -> bool:
        """验证RX90校准值。

        Args:
            rx90_x: X偏振RX90值。
            rx90_y: Y偏振RX90值。

        Returns:
            如果值在可接受范围内则返回True。
        """
        return (RX90_VOLTAGE_LOWER_LIMIT <= rx90_x <= RX90_VOLTAGE_UPPER_LIMIT and
                RX90_VOLTAGE_LOWER_LIMIT <= rx90_y <= RX90_VOLTAGE_UPPER_LIMIT)

    def _save_rx90_success(self) -> None:
        """将成功的RX90校准保存到EEPROM。"""
        self.cim8.excecute_mcm_and_disp_result("ESET", "PIC_EEPROM_HDR", [1, EEPROM_HEADER_VALUE])
        logger.info('RX90校准成功完成')

    def _adjust_rx90_values(self, rec_rx90_x: float, rec_rx90_y: float) -> None:
        """如果RX90值超出可接受范围则进行调整。

        Args:
            rec_rx90_x: 记录的X偏振RX90值。
            rec_rx90_y: 记录的Y偏振RX90值。
        """
        # 计算调整后的值
        rx90_x = self._calculate_adjusted_rx90_value(rec_rx90_x)
        rx90_y = self._calculate_adjusted_rx90_value(rec_rx90_y)

        # 应用调整
        self._apply_rx90_adjustments(rx90_x, rx90_y)

    def _calculate_adjusted_rx90_value(self, value: float) -> float:
        """基于限制计算调整后的RX90值。

        Args:
            value: 原始RX90值。

        Returns:
            调整后的RX90值。
        """
        if value > RX90_VOLTAGE_UPPER_LIMIT:
            return math.sqrt(value ** 2 - RX90_VOLTAGE_ADJUSTMENT_OFFSET)
        elif value < RX90_VOLTAGE_LOWER_LIMIT:
            return math.sqrt(value ** 2 + RX90_VOLTAGE_ADJUSTMENT_OFFSET)
        else:
            return value

    def _apply_rx90_adjustments(self, rx90_x: float, rx90_y: float) -> None:
        """将RX90值调整应用到模块。

        Args:
            rx90_x: 调整后的X偏振RX90值。
            rx90_y: 调整后的Y偏振RX90值。
        """
        # 禁用RX90环路
        ticks = self.cim8.excecute_mcm_and_disp_result("get", "PIC_SW_CNTL_RX90", [1])
        self.cim8.excecute_mcm_and_disp_result("set", "PIC_SW_CNTL_RX90", [1, 0])

        # 更新RX90值
        self.cim8.excecute_mcm_and_disp_result("set", "BSP_DAC_REG", [1, "RX90X", rx90_x])
        self.cim8.excecute_mcm_and_disp_result("set", "BSP_DAC_REG", [1, "RX90Y", rx90_y])

        # 重新启用RX90环路
        self.cim8.excecute_mcm_and_disp_result("set", "PIC_SW_CNTL_RX90", [1, 1, 0, ticks])

        # 存储更新的设置
        self._store_rx90_settings_with_retry()

    def _store_rx90_settings_with_retry(self) -> None:
        """使用重试机制存储RX90设置。"""
        # 第一次尝试
        self.cim8.excecute_mcm_and_disp_result("ESET", "PIC_STORE_RX90_SETTINGS", [1, 1])
        post_reply = self.cim8.excecute_mcm_and_disp_result("get", "PIC_STORE_RX90_SETTINGS", [1])

        if not post_reply:
            # 延迟后第二次尝试
            time.sleep(2)
            self.cim8.excecute_mcm_and_disp_result("ESET", "PIC_STORE_RX90_SETTINGS", [1, 1])
            post_reply = self.cim8.excecute_mcm_and_disp_result("get", "PIC_STORE_RX90_SETTINGS", [1])

            if not post_reply:
                # 强制收敛的最终尝试
                self.cim8.excecute_mcm_and_disp_result("set", "PIC_FORCE_RX90_CNTL", [1, 1])
                time.sleep(3)  # 等待解调锁定经过时间 > 1秒
                self.cim8.excecute_mcm_and_disp_result("ESET", "PIC_STORE_RX90_SETTINGS", [1, 1])
                self.cim8.excecute_mcm_and_disp_result("get", "PIC_STORE_RX90_SETTINGS", [1])

    @staticmethod
    def cal_power(wllist: List[float], powerlist: List[float], freq_c: float, ch_width: float) -> float:
        """计算信号功率。

        Args:
            wllist: 波长列表（nm）
            powerlist: 功率列表（dBm）
            freq_c: 中心频率（GHz）
            ch_width: 信道宽度（GHz）

        Returns:
            信号功率（mW）
        """

        if freq_c < 200:
            freq_c = freq_c * 1000
        if freq_c > 200000:
            freq_c = freq_c / 1000
        C = 299792458
        dot_width = abs(wllist[0] - wllist[-1]) / (len(wllist) - 1)
        wl_left = C / (freq_c + ch_width / 2)
        wl_right = C / (freq_c - ch_width / 2)
        sig_wl = []
        sig_data = []
        for i in range(len(wllist)):
            wl = wllist[i]
            power = powerlist[i]
            if wl_left < wl < wl_right:
                sig_wl.append(wl)
                sig_data.append(10 ** (power / 10))
        left_corr = (sig_wl[0] - wl_left - dot_width / 2) / dot_width * sig_data[0]
        right_corr = (wl_right - sig_wl[-1] - dot_width / 2) / dot_width * sig_data[-1]
        signal_power = sum(sig_data + [left_corr, right_corr])
        return signal_power

    def get_osnr_fit(self, freq_c: float, ch_width: float) -> float:
        """获取OSNR拟合值。

        Args:
            freq_c: 中心频率（GHz）
            ch_width: 信道宽度（GHz）

        Returns:
            OSNR值
        """
        if freq_c < 200:
            freq_c = freq_c * 1000
        if freq_c > 200000:
            freq_c = freq_c / 1000
        C = 299792458
        sweep_start_wl = C / (freq_c + 2 * ch_width)
        sweep_stop_wl = C / (freq_c - 2 * ch_width)
        self.osa.setup(sweep_start_wl, sweep_stop_wl, 0.02)
        wavelen, power = self.osa.get_spectra()
        s_and_n = self.cal_power(wavelen, power, freq_c, ch_width)
        f_delta = (freq_c * freq_c) / C * 0.1
        # f_delta = 12.5
        n_l = self.cal_power(wavelen, power, freq_c - ch_width, f_delta)
        n_r = self.cal_power(wavelen, power, freq_c + ch_width, f_delta)
        n = (n_l + n_r) / 2
        s = s_and_n - n / 12.5 * ch_width
        osnr = 10 * math.log10(s / n)
        return osnr

    def get_tx_osnr(self, freq_c: float, ch_width: float) -> float:
        """获取TX OSNR值。

        Args:
            freq_c: 中心频率（GHz）
            ch_width: 信道宽度（GHz）

        Returns:
            OSNR值
        """
        self.osw_ase.switch_channel(self.osw_ase_off)
        time.sleep(0.5)
        osnr = self.get_osnr_fit(freq_c, ch_width)
        return osnr

    def check_tx_state(self) -> bool:
        """检查TX状态。

        Returns:
            TX状态是否正常
        """
        rx_state = self.cim8.CheckDemodLock()  # 获取RX状态是否达到解调器锁定
        if rx_state:
            self.rx90_calibration()
            return True
        else:
            logger.error('!!!RX_ADC_Output_Recovery 请检查rx状态')
            return False

    @staticmethod
    def evaluate_expression(expression: str) -> str:
        """将传入的字符串参数作为表达式返回。

        Args:
            expression: 表达式字符串

        Returns:
            原始表达式字符串
        """
        return expression

    @staticmethod
    def reset_time(reset_time_value: float) -> None:
        """根据复位时间值进行等待。

        Args:
            reset_time_value: 复位时间值
        """
        if reset_time_value < RESET_TIME_THRESHOLD_SECONDS:
            time.sleep(RESET_TIME_BUFFER_SECONDS - reset_time_value)

    def reset_module_and_wait(self, sleep_duration=3, ensure_ready_after_normal_power=True,
                              set_low_power_after_reset=False):
        """模块复位并可选地等待其就绪。

        该函数对模块执行软复位。可以选择在复位后引入延迟，
        确保模块在非低功耗模式下就绪，以及在复位后立即将模块设置为低功耗模式。

        注意:
            如果 `set_low_power_after_reset` 参数为 True, 则
            `ensure_ready_after_normal_power` 参数通常应为 False。
            这是因为模块通常在低功耗模式下进行配置，并在退出低功耗模式后检查其就绪状态。

        Args:
            sleep_duration (int, optional): 复位后休眠的时间（单位：秒）。
                默认为 3。
            ensure_ready_after_normal_power (bool, optional): 如果为 True 且模块未被设置为
                低功耗模式，则等待模块就绪。默认为 True。
            set_low_power_after_reset (bool, optional): 如果为 True，则在复位后立即将模块
                设置为低功耗模式。默认为 False。
        """
        self.cim8.SoftmodRst()  # 执行模块软复位
        if sleep_duration > 0:
            time.sleep(sleep_duration)  # 复位后延时

        if set_low_power_after_reset:
            self.cim8.lowpower(1)  # 设置模块为低功耗模式

        # 如果模块未设置为低功耗模式，并且要求确保模块在正常功耗模式下就绪，则等待模块就绪。
        if ensure_ready_after_normal_power and not set_low_power_after_reset:
            self.cim8.waite_for_module_ready()  # 等待模块达到就绪状态

    def module_mode_config(self, mode_type: str) -> bool:
        """设置模块模式

        Args:
            mode_type: 模式类型

        Returns:
            设置是否成功
        """
        self.cim8.lowpower(1)
        if mode_type == "400G_PURE_QPSK_ETH":
            DEV_RATE = 0x100  # Ethernet, 53.125G
            CL_INTF_0_5 = 0x6  # single 400GAUI-8
            CL_INTF_6_11 = 0x0
            CL_EN = 0x1  # only INTF0 enabled

            DATA_RATE = 4  # 400G
            MODE = 0  # uiniform
            MODEM = 0x121  # bin(0x121)：'0b100100001'(64symbols/DP-QPSK/15%overhead)
            BAUD_RATE = float2hex(118.71)
        elif mode_type == "800G_pcs_138GBd":
            DEV_RATE = 0X100  # ethernet 53.125g
            CL_INTF_0_5 = 0x222222  # 100GAUI-2
            CL_INTF_6_11 = 0x22  # 100GAUI-2
            CL_EN = 0xFF  # INTF0-7 enabled

            DATA_RATE = 8
            MODE = 1
            MODEM = 0X101  # auto mode
            BAUD_RATE = float2hex(137.9784)
        elif mode_type == "800G_PCSA_143G_ETH_PILOT32":
            DEV_RATE = 0X100  # ethernet 53.125g
            CL_INTF_0_5 = 0x222222  # 100GAUI-2
            CL_INTF_6_11 = 0x22  # 100GAUI-2
            CL_EN = 0xFF  # INTF0-7 enabled

            DATA_RATE = 8
            MODE = 1
            MODEM = 0X1  # auto mode,every 32 symbols
            BAUD_RATE = float2hex(143)
        else:
            logger.error(f"设置模块还不支持")
            return False
        # logger.info(f"开始配置客户端接口参数，参数为：DEV_RATE={DEV_RATE} CL_INTF_0_5={CL_INTF_0_5} "
        #             f"CL_INTF_6_11={CL_INTF_6_11} CL_EN={CL_EN}")
        try:
            logger.info(f"开始配置客户端接口参数，参数为：DEV_RATE={DEV_RATE} CL_INTF_0_5={CL_INTF_0_5} "
                        f"CL_INTF_6_11={CL_INTF_6_11} CL_EN={CL_EN}")
            # 配置客户端接口参数
            self.cim8_func.Client_ConfigReq(
                DEV_RATE=DEV_RATE,
                CL_INTF_0_5=CL_INTF_0_5,
                CL_INTF_6_11=CL_INTF_6_11,
                CL_EN=CL_EN
            )
            logger.info(f"开始配置网络接口参数，参数为：DATA_RATE={DATA_RATE} MODE={MODE} MODEM={MODEM} "
                        f"BAUD_RATE={BAUD_RATE}")
            # 配置网络接口参数
            self.cim8_func.Ntwk_ConfigReq(
                DATA_RATE=DATA_RATE,
                MODE=MODE,
                MODEM=MODEM,
                BAUD_RATE=BAUD_RATE)
        except Exception as e:
            logger.error(f"设置模块模式失败！具体原因为：{e}")
            return False
        self.cim8.lowpower(0)  # 退出低功耗
        return self.cim8.waite_for_module_ready()  # 等待模块达到就绪状态

    def _configure_tx_freq_power(self, freq: Optional[float] = None, power: Optional[float] = None) -> None:
        """配置发射机的频率和/或功率。

        如果某个参数为 None，则相应的设置不会被更改。

        Args:
            freq: 要设置的频率（例如，单位 THz）。
                如果为 None，则不改变当前频率设置。默认为 None。
            power: 要设置的功率（例如，单位 dBm）。
                如果为 None，则不改变当前功率设置。默认为 None。
        """
        if freq is not None:
            self.cim8.set_freq(freq)  # 设置发射频率
        if power is not None:
            self.cim8.set_tx_pow(power)  # 设置发射功率

    def use_opm_set_rx_power(self, target_rx_power: float, precise: float) -> float:
        """使用OPM设置RX为指定值。

        Args:
            target_rx_power: 期望的RX值
            precise: 误差范围

        Returns:
            最终的RX功率值
        """
        # 获取模块当前频率
        freq0 = self.cim8.get_freq()

        # 关闭ase
        self.osw_ase.switch_channel(self.osw_ase_off)

        time.sleep(0.5)

        opm_rx = self.opm.read_power_dbm() + self.rx_offset[freq0]

        start_time = time.time()
        while True:
            end_time = time.time()
            if end_time - start_time > 60 * 3:
                logger.error(f"超过3分钟还没有将入光调整到目标值，最终的rx值为：{opm_rx}")
                return opm_rx
            opm_rx = self.opm.read_power_dbm() + self.rx_offset[freq0]
            # opm_rx = self.opm.read_power_dbm()
            deltas = opm_rx - target_rx_power
            att = self.voa_rx.get_attr()
            logger.info(f'OPM:{opm_rx},ATT:{att}')
            if abs(deltas) > precise:
                att += deltas * 0.9
                self.voa_rx.set_attr(att)
                time.sleep(0.5)
            else:
                return opm_rx

    def set_rx_power(self, target_rx_power: float, osw_tr_sleep: int = 1, precise: float = 0.5) -> float:
        """设置RX为指定值。

        Args:
            target_rx_power: 期望的RX值
            osw_tr_sleep: 切换osw_tr后休眠的时间（单位：秒）。默认为1。如果设置为0或负数，则不进行休眠
            precise: 误差范围

        Returns:
            实际的RX功率值
        """
        # 接入opm
        self.osw_tr.switch_channel(self.osw_tr_rx_pow)
        # 通过调整voa使得rx为rx_power
        actual_rx_power = self.use_opm_set_rx_power(target_rx_power, precise)
        # 光路接入rx
        self.osw_tr.switch_channel(self.osw_tr_tx_pow)
        # 等待
        if osw_tr_sleep > 0:
            time.sleep(osw_tr_sleep)  # 等待指定时间

        return actual_rx_power

    def set_to_hipwr(self) -> None:
        """将模块设置为高功率配置 (POST_TU_CONFIG)。"""
        # 将模块的 <HiPwr_Config> 设置为 POST_TU_CONFIG
        # 操作寄存器 0x1E210
        reg_addr1 = 0x1E210
        bits_to_clear1 = [2]
        original_value_reg1 = self.cim8.get_reg(reg_addr1, 1)  # 读取寄存器原始值
        modified_value_reg1 = self.clear_bits(original_value_reg1, bits_to_clear1)  # 清除指定位
        self.cim8.set_reg(reg_addr1, modified_value_reg1)  # 写回修改后的值

        # 操作寄存器 0x02008
        reg_addr2 = 0x02008
        bits_to_clear2 = [20, 19, 15, 14, 13, 12, 11]
        original_value_reg2 = self.cim8.get_reg(reg_addr2, 1)  # 读取寄存器原始值
        modified_value_reg2 = self.clear_bits(original_value_reg2, bits_to_clear2)  # 清除指定位
        self.cim8.set_reg(reg_addr2, modified_value_reg2)  # 写回修改后的值

    def set_tof_voa_control_loops(self, enable: bool) -> None:
        """启用或禁用 TOF/VOA 用户控制环路。

        通过设置 `PIC_TOF_USER_CNTL_DIS` 寄存器的特定索引来控制
        X偏振/输入 (IN) 和 Y偏振/输出 (OUT) 的TOF/VOA用户控制环路。

        Args:
            enable: 如果为 True，则启用控制环路；如果为 False，则禁用控制环路。
                   值为 True 时，对应寄存器位设置为0（使能）；
                   值为 False 时，对应寄存器位设置为1（禁止）。
        """
        val = 0 if enable else 1  # enable 为 True 时 val 为 0 (启用)，为 False 时 val 为 1 (禁用)

        # 首先操作 Y-pol/OUT
        self.cim8.excecute_mcm_and_disp_result('SET', 'PIC_TOF_USER_CNTL_DIS', [1, 2, val])
        # 然后操作 X-pol/IN
        self.cim8.excecute_mcm_and_disp_result('SET', 'PIC_TOF_USER_CNTL_DIS', [1, 1, val])

    def setup_tx_power_control(self, tx_power_stab_time: float, target_power: int = 0x5) -> None:
        """配置并稳定tx功率控制。

        Args:
            tx_power_stab_time: 等待功率稳定的时间（单位：秒）。
            target_power: 目标tx功率设置，默认为0x5。
        """
        self.cim8.excecute_mcm_and_disp_result('SET', 'TXPWR_CTRL_ENA', [1, 0])  # 禁用发送功率控制环路
        self.cim8.excecute_mcm_and_disp_result('SET', 'TXPWR_CTRL_PWR', [1, target_power])  # 设置目标发送功率
        self.cim8.excecute_mcm_and_disp_result('SET', 'TXPWR_CTRL_ENA', [1, 1])  # 启用发送功率控制环路
        if tx_power_stab_time > 0:
            time.sleep(tx_power_stab_time)  # 等待功率稳定

    def tof_calibration_store_and_get_peaks(self) -> List[float]:
        """存储TOF的初始峰值位置并检索校准后的TOF值。

        此函数执行一系列操作来触发模块内部的TOF（可调光滤波器）初始值存储机制，
        并在发射机重新稳定后读取这些已存储（可能经过内部微调或校准）的TOF值。

        Returns:
            list: 一个列表，包含从模块获取的校准后的TOF值。列表的具体内容和结构
                  取决于 'PIC_TOF_STORE_TOF_INIT' 命令的 'EGET' 操作返回的数据格式。
                  通常，这会是与TOFIN和TOFOUT相关的多个值。
        """
        # 指示模块存储当前的TOF设置作为初始参考
        self.cim8.excecute_mcm_and_disp_result('SET', 'PIC_TOF_STORE_TOF_INIT', [1])

        # 关闭再打开TX，并等待模块就绪，以确保TOF值被正确处理和存储
        self.cim8.tx_disable()  # 关闭tx (可能操作寄存器 0x02000)
        self.cim8.tx_enable()  # 重新开启tx
        self.cim8.waite_for_module_ready()  # 等待模块达到就绪状态 (可能检查寄存器 0x02208)
        time.sleep(3)  # 额外延时以确保稳定

        # 获取已存储（并可能已校准）的TOF值
        TOFIN_TOFOUT_list = self.cim8.excecute_mcm_and_disp_result('EGET', 'PIC_TOF_STORE_TOF_INIT', [1])
        logger.info(f'Record calibrated TOF values:   {TOFIN_TOFOUT_list}')  # 打印记录的校准TOF值

    def get_tof_voa_peak_locations(self) -> Tuple[float, float, Dict[str, Tuple[float, float]],
                                          Dict[str, Tuple[float, float]], float, float, float, float]:
        """检索TOF和VOA的峰值位置（DAC设置）及相关的参考参数。

        Returns:
            包含以下元素的元组：
                - TEMP_REF: PIC的参考温度（例如摄氏度）
                - Freq_REF: 参考频率（例如THz）
                - tof_dict_X: 包含X偏振各TOF的mA值和mA值的平方的字典
                - tof_dict_Y: 包含Y偏振各TOF的mA值和mA值的平方的字典
                - VOAX_mA: PIC_TOFINVOA (X偏振VOA) 的DAC值 (mA)
                - VOAX_Max: PIC_TOFINVOA DAC值的平方
                - VOAY_mA: PIC_TOFOUTVOA (Y偏振VOA) 的DAC值 (mA)
                - VOAY_Max: PIC_TOFOUTVOA DAC值的平方
        """
        # 读取X偏振 (输入路径) TOF 和 VOA 的DAC值及其平方值
        TOF1X_mA = self.cim8.BSP_DAC_REG("PIC_TOFIN1")
        TOF1X_Max = TOF1X_mA ** 2
        TOF2X_mA = self.cim8.BSP_DAC_REG("PIC_TOFIN2")
        TOF2X_Max = TOF2X_mA ** 2
        TOF3X_mA = self.cim8.BSP_DAC_REG("PIC_TOFIN3")
        TOF3X_Max = TOF3X_mA ** 2
        TOF4X_mA = self.cim8.BSP_DAC_REG("PIC_TOFIN4")
        TOF4X_Max = TOF4X_mA ** 2
        TOF5X_mA = self.cim8.BSP_DAC_REG("PIC_TOFIN5")
        TOF5X_Max = TOF5X_mA ** 2
        TOF6X_mA = self.cim8.BSP_DAC_REG("PIC_TOFIN6")
        TOF6X_Max = TOF6X_mA ** 2
        TOF7X_mA = self.cim8.BSP_DAC_REG("PIC_TOFIN7")
        TOF7X_Max = TOF7X_mA ** 2
        VOAX_mA = self.cim8.BSP_DAC_REG("PIC_TOFINVOA")
        VOAX_Max = VOAX_mA ** 2

        # 读取Y偏振 (输出路径) TOF 和 VOA 的DAC值及其平方值
        TOF1Y_mA = self.cim8.BSP_DAC_REG("PIC_TOFOUT1")
        TOF1Y_Max = TOF1Y_mA ** 2
        TOF2Y_mA = self.cim8.BSP_DAC_REG("PIC_TOFOUT2")
        TOF2Y_Max = TOF2Y_mA ** 2
        TOF3Y_mA = self.cim8.BSP_DAC_REG("PIC_TOFOUT3")
        TOF3Y_Max = TOF3Y_mA ** 2
        TOF4Y_mA = self.cim8.BSP_DAC_REG("PIC_TOFOUT4")
        TOF4Y_Max = TOF4Y_mA ** 2
        TOF5Y_mA = self.cim8.BSP_DAC_REG("PIC_TOFOUT5")
        TOF5Y_Max = TOF5Y_mA ** 2
        TOF6Y_mA = self.cim8.BSP_DAC_REG("PIC_TOFOUT6")
        TOF6Y_Max = TOF6Y_mA ** 2
        TOF7Y_mA = self.cim8.BSP_DAC_REG("PIC_TOFOUT7")
        TOF7Y_Max = TOF7Y_mA ** 2
        VOAY_mA = self.cim8.BSP_DAC_REG("PIC_TOFOUTVOA")
        VOAY_Max = VOAY_mA ** 2

        tof_dict_X = {
            "PIC_TOFIN1": (TOF1X_mA, TOF1X_Max), "PIC_TOFIN2": (TOF2X_mA, TOF2X_Max),
            "PIC_TOFIN3": (TOF3X_mA, TOF3X_Max), "PIC_TOFIN4": (TOF4X_mA, TOF4X_Max),
            "PIC_TOFIN5": (TOF5X_mA, TOF5X_Max), "PIC_TOFIN6": (TOF6X_mA, TOF6X_Max),
            "PIC_TOFIN7": (TOF7X_mA, TOF7X_Max)
        }
        tof_dict_Y = {
            "PIC_TOFOUT1": (TOF1Y_mA, TOF1Y_Max), "PIC_TOFOUT2": (TOF2Y_mA, TOF2Y_Max),
            "PIC_TOFOUT3": (TOF3Y_mA, TOF3Y_Max), "PIC_TOFOUT4": (TOF4Y_mA, TOF4Y_Max),
            "PIC_TOFOUT5": (TOF5Y_mA, TOF5Y_Max), "PIC_TOFOUT6": (TOF6Y_mA, TOF6Y_Max),
            "PIC_TOFOUT7": (TOF7Y_mA, TOF7Y_Max)
        }

        return tof_dict_X, tof_dict_Y, VOAX_mA, VOAX_Max, VOAY_mA, VOAY_Max

    def get_tx_power(self, freq: float) -> float:
        """获取TX功率。

        Args:
            freq: 频率值

        Returns:
            校正后的TX功率值
        """
        self.osw_tr.switch_channel(self.osw_tr_tx_pow)
        time.sleep(1)
        power = self.opm.read_power_dbm() + self.tx_offset[freq]
        # power = self.opm.read_power_dbm()
        logger.info(f'get tx power:{power}')
        return power

    def calibrate_x_pol_voa(self, tof_dict_X: dict, VOAX_mA: float, tof_dict_Y: dict,
                            VOAY_Max: float, VOA_v_start: float, VOA_v_stepsize: float):
        """校准X偏振路径的VOA（可变光衰减器）。

            此函数通过一系列精密的设置和扫描来确定X偏振VOA的最佳工作点（峰值）
            及其干涉条纹宽度（Fringe）。校准过程旨在优化光信号通过X偏振路径的传输。

            Args:
                tof_dict_X (dict): 包含X偏振各TOF名称到其 (mA峰值, mA峰值平方) 元组映射的字典。
                VOAX_mA (float): X偏振VOA (`PIC_TOFINVOA`) 的初始峰值DAC设置 (mA)。
                tof_dict_Y (dict): 包含Y偏振各TOF名称到其 (mA峰值, mA峰值平方) 元组映射的字典。
                VOAY_Max (float): Y偏振VOA (`PIC_TOFOUTVOA`) 峰值DAC设置的平方 (mA²)。
                VOA_v_start (float): X偏振VOA扫描的起始设置值 (mA²)。
                VOA_v_stepsize (float): X偏振VOA扫描的步长 (mA²)。

            Returns:
                tuple[float, float]: 一个元组，包含：
                    - `VOA_PEAK_X` (float): 校准得到的X偏振VOA峰值设置 (mA)。
                    - `FringeX` (float): 校准得到的X偏振VOA干涉条纹宽度 (mA²)。
        """
        VOA_v_start = float(VOA_v_start)
        VOA_v_stepsize = float(VOA_v_stepsize)
        # 步骤1: 设置X偏振路径的TOF和VOA到其峰值
        for key, values in tof_dict_X.items():
            self.cim8.excecute_mcm_and_disp_result('SET', 'BSP_DAC_REG', [1, key, values[0]])
        self.cim8.excecute_mcm_and_disp_result('SET', 'BSP_DAC_REG', [1, 'PIC_TOFINVOA', VOAX_mA])

        # 步骤2: 最小化Y偏振路径的串扰，通过寻找并设置Y-VOA的实际零点
        # 将Y-TOF设置为估算的零点附近
        for key, values in tof_dict_Y.items():
            # values[1] 是 mA_squared_value, 3.25 是经验偏移量 (mA^2)
            estimated_y_tof_null_mA = math.sqrt(values[1] + 3.25)
            self.cim8.excecute_mcm_and_disp_result('SET', 'BSP_DAC_REG', [1, key, estimated_y_tof_null_mA])

        # 扫描Y-VOA找到实际零点
        VOAY_NULL_mA = 0.0  # 初始化Y-VOA零点对应的mA值
        mpd_min = self.cim8.BSP_ADC_REG('PIC_TOFPDPOST_Y')  # 读取Y偏振光电二极管的初始值

        for setpoint_offset_ma2 in self.Null_v:  # Null_v 中的值是相对于VOAY_Max的mA^2偏移
            VOAY_scan_val_mA = math.sqrt(VOAY_Max + setpoint_offset_ma2)
            self.cim8.excecute_mcm_and_disp_result('SET', 'BSP_DAC_REG', [1, 'PIC_TOFOUTVOA', VOAY_scan_val_mA])
            time.sleep(0.1)  # 短暂延时等待设置生效
            current_mpd_y = self.cim8.BSP_ADC_REG('PIC_TOFPDPOST_Y')
            if current_mpd_y < mpd_min:
                VOAY_NULL_mA = VOAY_scan_val_mA
                mpd_min = current_mpd_y

        self.cim8.excecute_mcm_and_disp_result('SET', 'BSP_DAC_REG',
                                               [1, 'PIC_TOFOUTVOA', VOAY_NULL_mA])  # 设置Y-VOA到找到的零点

        # 步骤3: 扫描X偏振VOA (PIC_TOFINVOA) 以确定Fringe和峰值
        voa_settings_ma2_list, tx_power_list = [], []
        self.osw_tr.switch_channel(self.osw_tr_tx_pow)  # 切换光开关以测量TX功率
        _ = self.cim8.get_freq()  # 调用可能用于确保模块状态或触发内部逻辑，返回值未使用
        # VOA_v_start 和 VOA_v_stepsize 单位是 mA^2
        for i in range(500):  # 最多扫描500个点
            current_voax_setting_ma2 = VOA_v_start + float(VOA_v_stepsize) * i
            current_voax_setting_mA = math.sqrt(current_voax_setting_ma2)
            logger.info(
                f"VOA_v_start:{VOA_v_start} current_voax_setting_ma2:{current_voax_setting_ma2}"
                f"current_voax_setting_mA: {current_voax_setting_mA}")
            self.cim8.excecute_mcm_and_disp_result('SET', 'BSP_DAC_REG', [1, 'PIC_TOFINVOA', current_voax_setting_mA])
            time.sleep(1)  # 等待功率稳定

            txpwr = self.get_tx_power(self.cim8.freq)

            # 通常在干涉谷底（零点附近）功率会显著下降
            if txpwr < -18:  # -20 dBm作为判断接近零点的阈值
                voa_settings_ma2_list.append(round(current_voax_setting_ma2, 2))
                tx_power_list.append(txpwr)
            if txpwr > -15:
                VOA_v_start += 0.5

            # 如果找到了至少两个极小值点（代表两个干涉零点），则停止扫描
            if len(find_extrema(tx_power_list)) >= 2:
                logger.info(voa_settings_ma2_list)
                logger.info(tx_power_list)
                break
        if len(voa_settings_ma2_list) < 2 or len(find_extrema(tx_power_list)) < 2:
            print("警告：未能找到足够的X-VOA零点数据来计算Fringe和Peak。")
            # 根据实际需求，这里可能需要返回错误或默认值
            return VOAX_mA, 0.0  # 返回初始峰值和0 Fringe作为错误指示
        logger.info(f"voa_list:{voa_settings_ma2_list}\ntxpwr_list:{tx_power_list}")
        point1_idx, point2_idx = find_extrema(tx_power_list)[0][0], find_extrema(tx_power_list)[1][0]
        NULLx1_ma2 = voa_settings_ma2_list[point1_idx]
        NULLx2_ma2 = voa_settings_ma2_list[point2_idx]

        FringeX_ma2 = NULLx2_ma2 - NULLx1_ma2  # Fringe宽度 (mA²)
        # 峰值通常在两个零点的中间（相位意义上），或者取其中一个零点加上半个Fringe
        # 此处假设峰值靠近第一个零点加上半个Fringe的平方根
        VOA_PEAK_X_mA = math.sqrt(NULLx1_ma2 + 0.5 * FringeX_ma2)

        return VOA_PEAK_X_mA, FringeX_ma2

    def calibrate_y_pol_voa(self,
                            tof_dict_X: dict,
                            tof_dict_Y: dict,
                            VOAX_mA: float,
                            VOAY_mA: float,
                            VOAX_Max: float,
                            VOA_v_start: float,
                            VOA_v_stepsize: float
                            ):
        """校准Y偏振路径的VOA（可变光衰减器）。

        此函数通过一系列精密的设置和扫描来确定Y偏振VOA的最佳工作点（峰值）
        及其干涉条纹宽度（Fringe）。校准过程与X偏振VOA的校准过程对称。

        主要校准步骤:
        1.  **还原所有TOF和VOA至峰值状态**:
            - 将X偏振和Y偏振的所有TOF（`PIC_TOFINx`, `PIC_TOFOUTx`）设置为其已知的峰值DAC值。
            - 将X偏振VOA (`PIC_TOFINVOA`) 和Y偏振VOA (`PIC_TOFOUTVOA`) 也设置为其初始峰值DAC值。

        2.  **最小化X偏振路径串扰**:
            - 将X偏振的各个TOF设置为估算的零点附近 (通过在其mA值的平方上加一个小的偏移量 `3.25` mA²，然后开方得到mA值)。
            - 通过扫描X偏振VOA（`PIC_TOFINVOA`）周围的一系列预设点 (`Null_v`，单位mA²，
              相对于其峰值 `VOAX_Max`），并监测X偏振的光电二极管读数（`PIC_TOFPDPOST_X`），
              找到使该读数最小的X-VOA设置。此设置 (`VOAX_NULL_mA`) 被认为是X偏振路径
              在此条件下的实际零点，从而最大限度地减少其对Y偏振测量的影响。

        3.  **扫描Y偏振VOA以确定Fringe和峰值**:
            - 初始化用于存储Y偏振VOA扫描值（mA²）和对应发射光功率（dBm）的列表。
            - （假定光开关已正确设置或在此函数外处理，原X-pol校准中有 `ts.switch_osw_tr(1)`，此处Y-pol扫描前未见对应调用）。
            - （可选地调用 `module.get_freq()`，可能用于同步或状态检查，但其返回值在此处未被使用）。
            - 进行迭代扫描（最多500次）：
                - 根据起始值 `VOA_v_start` (mA²) 和步长 `VOA_v_stepsize` (mA²) 计算
                  当前Y偏振VOA的设置值（转换为mA值后设置给 `PIC_TOFOUTVOA`）。
                - 等待1秒。
                - 读取发射光功率。
                - 如果光功率低于-20 dBm，记录VOA设置值（mA²）和光功率。
                - 使用 `find_extrema` 分析功率列表，找到至少两个极小值点后停止。
            - 根据找到的两个零点位置 (`NULLy1`, `NULLy2`，单位mA²），计算：
                - 干涉条纹宽度: `FringeY = abs(NULLy2 - NULLy1)` (mA²)。 (使用abs确保正值)
                - Y偏振VOA的峰值设置: `VOA_PEAK_Y = math.sqrt(min(NULLy1, NULLy2) + 0.5 * FringeY)` (mA)。 (取较小零点加半个Fringe)

        Args:
            tof_dict_X (dict): 包含X偏振各TOF名称到其 (mA峰值, mA峰值平方) 元组映射的字典。
            tof_dict_Y (dict): 包含Y偏振各TOF名称到其 (mA峰值, mA峰值平方) 元组映射的字典。
            VOAX_mA (float): X偏振VOA (`PIC_TOFINVOA`) 的峰值DAC设置 (mA)。
            VOAY_mA (float): Y偏振VOA (`PIC_TOFOUTVOA`) 的初始峰值DAC设置 (mA)。
            VOAX_Max (float): X偏振VOA (`PIC_TOFINVOA`) 峰值DAC设置的平方 (mA²)。
            VOA_v_start (float): Y偏振VOA扫描的起始设置值 (mA²)。
            VOA_v_stepsize (float): Y偏振VOA扫描的步长 (mA²)。

        Returns:
            tuple[float, float]: 一个元组，包含：
                - `VOA_PEAK_Y` (float): 校准得到的Y偏振VOA峰值设置 (mA)。
                - `FringeY` (float): 校准得到的Y偏振VOA干涉条纹宽度 (mA²)。
        """
        VOA_v_stepsize = float(VOA_v_stepsize)

        # 步骤1: 还原所有TOF和VOA到其峰值点
        for key, values in tof_dict_X.items():
            self.cim8.excecute_mcm_and_disp_result('SET', 'BSP_DAC_REG', [1, key, values[0]])
        for key, values in tof_dict_Y.items():
            self.cim8.excecute_mcm_and_disp_result('SET', 'BSP_DAC_REG', [1, key, values[0]])
        self.cim8.excecute_mcm_and_disp_result('SET', 'BSP_DAC_REG', [1, 'PIC_TOFINVOA', VOAX_mA])
        self.cim8.excecute_mcm_and_disp_result('SET', 'BSP_DAC_REG', [1, 'PIC_TOFOUTVOA', VOAY_mA])

        # 步骤2: 最小化X偏振路径的串扰，通过寻找并设置X-VOA的实际零点
        # 将X-TOF设置为估算的零点附近
        for key, values in tof_dict_X.items():
            estimated_x_tof_null_mA = math.sqrt(values[1] + 3.25)  # values[1] is mA_squared_value
            self.cim8.excecute_mcm_and_disp_result('SET', 'BSP_DAC_REG', [1, key, estimated_x_tof_null_mA])

        # 扫描X-VOA找到实际零点
        VOAX_NULL_mA = 0.0
        mpd_min = self.cim8.BSP_ADC_REG('PIC_TOFPDPOST_X')  # 读取X偏振光电二极管的初始值

        for setpoint_offset_ma2 in self.Null_v:  # Null_v 中的值是相对于VOAX_Max的mA^2偏移
            VOAX_scan_val_mA = math.sqrt(VOAX_Max + setpoint_offset_ma2)
            self.cim8.excecute_mcm_and_disp_result('SET', 'BSP_DAC_REG', [1, 'PIC_TOFINVOA', VOAX_scan_val_mA])
            time.sleep(0.1)  # 短暂延时
            current_mpd_x = self.cim8.BSP_ADC_REG('PIC_TOFPDPOST_X')
            if current_mpd_x < mpd_min:
                VOAX_NULL_mA = VOAX_scan_val_mA
                mpd_min = current_mpd_x

        self.cim8.excecute_mcm_and_disp_result('SET', 'BSP_DAC_REG', [1, 'PIC_TOFINVOA', VOAX_NULL_mA])

        # 步骤3: 扫描Y偏振VOA (PIC_TOFOUTVOA) 以确定Fringe和峰值
        voa_settings_ma2_list, tx_power_list = [], []
        # 注意：此处未显式调用 ts.switch_osw_tr()，假设光路已由调用此函数前的 _calibrate_x_pol_voa 或其他部分正确设置
        # _ = module.get_freq() # 调用可能用于确保模块状态，返回值未使用
        if VOA_v_start != 0:
            VOA_v_start = 0
        for i in range(500):  # 最多扫描500个点
            current_voay_setting_ma2 = VOA_v_start + VOA_v_stepsize * i
            current_voay_setting_mA = math.sqrt(current_voay_setting_ma2)

            self.cim8.excecute_mcm_and_disp_result('SET', 'BSP_DAC_REG', [1, 'PIC_TOFOUTVOA', current_voay_setting_mA])
            time.sleep(1)  # 等待功率稳定

            txpwr = self.get_tx_power(self.cim8.freq)

            if txpwr < -20:  # -20 dBm作为判断接近零点的阈值
                voa_settings_ma2_list.append(round(current_voay_setting_ma2, 2))
                tx_power_list.append(txpwr)
            if txpwr < -15 and i == 0:
                VOA_v_start += 2

            if txpwr > -15:
                VOA_v_start += 0.5

            if len(find_extrema(tx_power_list)) >= 2:
                break

        if len(voa_settings_ma2_list) < 2 or len(find_extrema(tx_power_list)) < 2:
            print("警告：未能找到足够的Y-VOA零点数据来计算Fringe和Peak。")
            return VOAY_mA, 0.0  # 返回初始峰值和0 Fringe作为错误指示
        logger.info(f"voa_list:{voa_settings_ma2_list}\ntxpwr_list:{tx_power_list}")

        # 确保point1和point2的索引在voa_settings_ma2_list的有效范围内
        extrema_indices = [p[0] for p in find_extrema(tx_power_list)]
        if not extrema_indices or max(extrema_indices) >= len(voa_settings_ma2_list):
            print("警告：find_extrema 返回的索引超出了voa_settings_ma2_list的范围。")
            return VOAY_mA, 0.0

        point1_idx = extrema_indices[0]
        point2_idx = extrema_indices[1]  # Assumes at least two extrema were found and are valid

        NULLy1_ma2 = voa_settings_ma2_list[point1_idx]
        NULLy2_ma2 = voa_settings_ma2_list[point2_idx]

        FringeY_ma2 = NULLy2_ma2 - NULLy1_ma2  # Fringe宽度 (mA²), 使用abs确保正值
        # 峰值通常在两个零点的中间（相位意义上），或者取其中一个零点加上半个Fringe
        VOA_PEAK_Y_mA = math.sqrt(NULLy1_ma2 + 0.5 * FringeY_ma2)

        return VOA_PEAK_Y_mA, FringeY_ma2

    def restore_tof_voa_and_module_control(self,
                                           tof_dict_X: dict,
                                           tof_dict_Y: dict,
                                           VOAX_mA: float,
                                           VOAY_mA: float
                                           ):
        """恢复TOF和VOA的DAC设置到峰值，并重新启用模块控制环路。

        在VOA（可变光衰减器）校准的开环扫描操作完成后，此函数用于：
        1. 将X偏振和Y偏振的所有TOF（可调光滤波器）DAC恢复到它们各自的峰值设置。
           这些峰值通常是在之前的步骤中确定或从 `tof_dict_X` 和 `tof_dict_Y` 中获取的。
        2. 将X偏振VOA (`PIC_TOFINVOA`) 和Y偏振VOA (`PIC_TOFOUTVOA`) 的DAC恢复到
           它们各自校准后的峰值设置 (`VOAX_mA`, `VOAY_mA`)。
        3. 调用 `_set_tof_voa_control_loops(enable=True)` 重新启用TOF/VOA的用户控制环路，
           使模块恢复到闭环控制状态。
        4. 等待3秒，以确保所有设置生效并且控制环路稳定下来。

        Args:
            tof_dict_X (dict): 包含X偏振各TOF名称到其 (mA峰值, mA峰值平方) 元组映射的字典。
                               `values[0]` 即为mA峰值。
            tof_dict_Y (dict): 包含Y偏振各TOF名称到其 (mA峰值, mA峰值平方) 元组映射的字典。
                               `values[0]` 即为mA峰值。
            VOAX_mA (float): X偏振VOA (`PIC_TOFINVOA`) 校准后的峰值DAC设置 (mA)。
            VOAY_mA (float): Y偏振VOA (`PIC_TOFOUTVOA`) 校准后的峰值DAC设置 (mA)。
        """
        # 恢复X偏振TOF DACs到峰值
        for key, values in tof_dict_X.items():
            self.cim8.excecute_mcm_and_disp_result('SET', 'BSP_DAC_REG', [1, key, values[0]])

        # 恢复Y偏振TOF DACs到峰值
        for key, values in tof_dict_Y.items():
            self.cim8.excecute_mcm_and_disp_result('SET', 'BSP_DAC_REG', [1, key, values[0]])

        # 恢复X偏振和Y偏振VOA DACs到峰值
        self.cim8.excecute_mcm_and_disp_result('SET', 'BSP_DAC_REG', [1, 'PIC_TOFINVOA', VOAX_mA])
        self.cim8.excecute_mcm_and_disp_result('SET', 'BSP_DAC_REG', [1, 'PIC_TOFOUTVOA', VOAY_mA])

        # 重新启用TOF/VOA用户控制环路
        self.set_tof_voa_control_loops(enable=True)
        time.sleep(3)  # 等待设置生效和环路稳定

    def save_tof_voa_calibration_to_eeprom(self,
                                           VOA_PEAK_X: float,
                                           VOA_PEAK_Y: float,
                                           FringeX: float,
                                           FringeY: float,
                                           Freq_REF: float,
                                           TEMP_REF: float
                                           ):
        """将TOF和VOA校准数据保存到EEPROM并复位模块。

        此函数负责将在TOF（可调光滤波器）和VOA（可变光衰减器）校准过程中确定的
        关键参数写入模块的EEPROM（电可擦可编程只读存储器）。这些参数包括：
        - X偏振和Y偏振VOA的校准峰值 (`VOA_PEAK_X`, `VOA_PEAK_Y`)。
        - X偏振和Y偏振VOA的拟合曲线系数，这些系数可能用于内部算法以补偿VOA响应。
          （例如，`1.57` 约等于 π/2，`3.14 / FringeX` 约等于 π/FringeX，可能与相位和周期有关）。
        - X偏振和Y偏振VOA的零点偏移量 (`0.5 * FringeX`, `0.5 * FringeY`)，单位为mA²。
        - 校准时的参考频率 (`Freq_REF`) 和参考PIC温度 (`TEMP_REF`)。

        在将所有数据写入EEPROM后，函数会触发一个命令以加载这些新校准数据到模块的
        活动配置中，然后调用 `_reset_module_and_wait` 函数对模块进行软复位，
        以确保新校准数据完全生效，并等待模块重新达到就绪状态。

        Args:
            VOA_PEAK_X (float): 校准得到的X偏振VOA峰值DAC设置 (单位: mA)。
                                (原注释为 sqrt(mA^2)，即 mA)。
            VOA_PEAK_Y (float): 校准得到的Y偏振VOA峰值DAC设置 (单位: mA)。
                                (原注释为 sqrt(mA^2)，即 mA)。
            FringeX (float): 校准得到的X偏振VOA干涉条纹宽度 (单位: mA²)。
            FringeY (float): 校准得到的Y偏振VOA干涉条纹宽度 (单位: mA²)。
            Freq_REF (float): 校准时的参考频率 (单位: THz)。
            TEMP_REF (float): 校准时的参考PIC（光子集成电路）温度 (单位: 摄氏度等)。
        """
        # 将X偏振和Y偏振VOA的校准峰值写入EEPROM
        # 参数: [1 (lane/channel), VOA_PEAK_X (mA), VOA_PEAK_Y (mA)]
        self.cim8.excecute_mcm_and_disp_result('ESET', 'PIC_TOF_STORE_VOA_INIT', [1, VOA_PEAK_X, VOA_PEAK_Y])

        # 将X偏振VOA的拟合曲线系数写入EEPROM
        # 参数: [1 (lane/channel), phase_offset (e.g., ~π/2), scale_factor (e.g., ~π/FringeX)]
        self.cim8.excecute_mcm_and_disp_result('ESET', 'PIC_TOF_VOAX_FITCURVE_COEFF', [1, 1.57, (3.14 / FringeX)])

        # 将Y偏振VOA的拟合曲线系数写入EEPROM
        self.cim8.excecute_mcm_and_disp_result('ESET', 'PIC_TOF_VOAY_FITCURVE_COEFF', [1, 1.57, (3.14 / FringeY)])

        # 将X偏振VOA的零点偏移量 (mA²) 写入EEPROM (通常是半个Fringe宽度)
        self.cim8.excecute_mcm_and_disp_result('ESET', 'PIC_TOF_VOAX_NULL_OFF_MA2', [1, 0.5 * FringeX])

        # 将Y偏振VOA的零点偏移量 (mA²) 写入EEPROM
        self.cim8.excecute_mcm_and_disp_result('ESET', 'PIC_TOF_VOAY_NULL_OFF_MA2', [1, 0.5 * FringeY])

        # 将校准时的参考频率写入EEPROM
        self.cim8.excecute_mcm_and_disp_result('ESET', 'PIC_TOF_VOA_F_REF', [1, Freq_REF])

        # 将校准时的参考PIC温度写入EEPROM
        self.cim8.excecute_mcm_and_disp_result('ESET', 'PIC_TOF_VOA_T_REF', [1, TEMP_REF])

        # 触发模块加载EEPROM中的校准数据到活动配置
        self.cim8.excecute_mcm_and_disp_result('GET', 'PIC_CAL_DATA_LOAD', [1])

        # 复位模块以使新的校准数据生效，并等待模块就绪
        self.reset_module_and_wait()

    def post_cal_initial_setup(self, TXPWR_Stab_time: float) -> Tuple[float, float]:
        """为TOFMPD（可调光滤波器监控光电二极管）后校准执行初始设置。

            此函数是TOFMPD后校准流程的第一步，主要负责准备模块状态：
            1.  调用 `module.waite_for_module_ready()` 确保模块处于就绪状态，可以接受后续指令。
            2.  从EEPROM中读取先前校准并存储的X偏振和Y偏振VOA的零点偏移量（Null Offset）。
                -   `VOAX_NULL_OFFSET`: 通过 'EGET' 命令从 'PIC_TOF_VOAX_NULL_OFF_MA2' 获取，单位通常为mA²。
                -   `VOAY_NULL_OFFSET`: 通过 'EGET' 命令从 'PIC_TOF_VOAY_NULL_OFF_MA2' 获取，单位通常为mA²。
            3.  调用 `_set_tof_voa_control_loops(enable=True)` 启用TOF/VOA的用户控制环路。
                这确保了在后续步骤中，TOF和VOA能够根据其闭环控制逻辑进行调整。
            4.  调用 `_setup_tx_power_control(tx_power_stab_time=TXPWR_Stab_time)` 配置并稳定
                发射（TX）功率。此步骤通常会将VOA驱动到其峰值功率点，为后续的MPD读数校准
                提供一个稳定的光功率基准。

            Args:
                TXPWR_Stab_time (float): 发射功率稳定所需的时间（单位：秒）。
                    此时间将传递给 `_setup_tx_power_control` 函数。

            Returns:
                tuple[float, float]: 一个包含两个浮点数的元组，依次为：
                    - `VOAX_NULL_OFFSET` (float): 从EEPROM读取的X偏振VOA零点偏移量 (mA²)。
                    - `VOAY_NULL_OFFSET` (float): 从EEPROM读取的Y偏振VOA零点偏移量 (mA²)。
            """
        self.cim8.waite_for_module_ready()  # 等待模块就绪

        # 从EEPROM获取X偏振和Y偏振VOA的零点偏移量 (mA²)
        # 假设 execute_mcm_and_disp_result 返回一个列表或元组，其中第一个元素是所需的值
        VOAX_NULL_OFFSET: float = self.cim8.excecute_mcm_and_disp_result('EGET', 'PIC_TOF_VOAX_NULL_OFF_MA2', [1])[0]
        VOAY_NULL_OFFSET: float = self.cim8.excecute_mcm_and_disp_result('EGET', 'PIC_TOF_VOAY_NULL_OFF_MA2', [1])[0]

        # 启用TOF/VOA用户控制环路
        self.set_tof_voa_control_loops(enable=True)

        # 设置并稳定TX功率，这通常会将VOA驱动到峰值
        self.setup_tx_power_control(tx_power_stab_time=TXPWR_Stab_time)

        return VOAX_NULL_OFFSET, VOAY_NULL_OFFSET

    def post_cal_x_pol(self,
                       tof_dict_X: dict,
                       VOAX_mA: float,
                       tof_dict_Y: dict,
                       VOAY_Max: float,
                       VOAY_NULL_OFFSET: float,
                       VOAX_Max: float,
                       VOAX_NULL_OFFSET: float
                       ) -> tuple[float, float]:
        """执行TOFMPD（可调光滤波器监控光电二极管）后校准的X偏振部分。

        Args:
            tof_dict_X (dict): 包含X偏振各TOF名称到其 (mA峰值, mA峰值平方) 元组映射的字典。
            VOAX_mA (float): X偏振VOA (`PIC_TOFINVOA`) 的峰值DAC设置 (mA)。
            tof_dict_Y (dict): 包含Y偏振各TOF名称到其 (mA峰值, mA峰值平方) 元组映射的字典。
            VOAY_Max (float): Y偏振VOA (`PIC_TOFOUTVOA`) 峰值DAC设置的平方 (mA²)。
            VOAY_NULL_OFFSET (float): Y偏振VOA的零点偏移量 (mA²)，从EEPROM读取。
            VOAX_Max (float): X偏振VOA (`PIC_TOFINVOA`) 峰值DAC设置的平方 (mA²)。
            VOAX_NULL_OFFSET (float): X偏振VOA的零点偏移量 (mA²)，从EEPROM读取。

        Returns:
            tuple[float, float]: 一个元组，包含：
                - `PostPDX_Slope` (float): X偏振PostPD响应的线性拟合斜率。
                - `PostPDX_Offset` (float): X偏振PostPD响应的线性拟合截距。
        """
        # 步骤1: 设置初始光路状态
        # 设置X偏振TOF和VOA到峰值
        for key, values in tof_dict_X.items():
            self.cim8.excecute_mcm_and_disp_result('SET', 'BSP_DAC_REG', [1, key, values[0]])
        self.cim8.excecute_mcm_and_disp_result('SET', 'BSP_DAC_REG', [1, 'PIC_TOFINVOA', VOAX_mA])

        # 设置Y偏振TOF到估算零点，Y-VOA到其零点偏移位置，以最小化串扰
        for key, values in tof_dict_Y.items():
            # values[1] is mA_squared_value (Y-TOF peak), 3.25 is an empirical offset (mA^2)
            estimated_y_tof_null_mA = math.sqrt(values[1] + 3.25)
            self.cim8.excecute_mcm_and_disp_result('SET', 'BSP_DAC_REG', [1, key, estimated_y_tof_null_mA])

        VOAY_NULL_mA = math.sqrt(VOAY_Max + VOAY_NULL_OFFSET)  # VOAY_Max is Y-VOA peak (mA^2)
        self.cim8.excecute_mcm_and_disp_result('SET', 'BSP_DAC_REG', [1, 'PIC_TOFOUTVOA', VOAY_NULL_mA])

        # 步骤2: 确定X偏振VOA的扫描范围
        X_null_point_ma2: float = VOAX_Max + VOAX_NULL_OFFSET  # X-VOA理论零点 (mA²)

        start_range_ma2: float = VOAX_Max  # 扫描起始点为X-VOA峰值 (mA²)
        step_size_ma2: float = 0.25 * VOAX_NULL_OFFSET  # 扫描步长 (mA²)
        # 扫描结束点略小于理论零点，以确保有足够光功率
        stop_range_ma2: float = X_null_point_ma2 - step_size_ma2 * 0.5

        # 生成扫描点序列 (mA²)
        VOAX_PostPD_v_ma2: np.ndarray = np.arange(round(start_range_ma2, 4), stop_range_ma2, step_size_ma2)

        # 步骤3: 执行扫描并收集数据
        TxPwrX_v_mw: list[float] = []
        PDPostX_v_adc: list[int] = []  # ADC读数通常为整数

        self.osw_tr.switch_channel(self.osw_tr_tx_pow)  # 切换光开关以测量TX功率
        _ = self.cim8.get_freq()  # 可能用于同步或状态检查

        logger.info("开始扫描X偏振VOA并收集PostPD数据...")
        for setpoint_ma2 in VOAX_PostPD_v_ma2:
            current_voax_setting_mA = math.sqrt(setpoint_ma2)
            self.cim8.excecute_mcm_and_disp_result('SET', 'BSP_DAC_REG', [1, 'PIC_TOFINVOA', current_voax_setting_mA])
            time.sleep(5)  # 等待功率稳定

            txpwr_dbm: float = self.get_tx_power(self.cim8.freq)
            txpwr_mw: float = 10 ** (txpwr_dbm / 10)
            # 假设 execute_mcm_and_disp_result 返回 [status, adc_value]
            pdpost_adc: int = self.cim8.excecute_mcm_and_disp_result('get', 'BSP_ADC_REG', [1, 'PIC_TOFPDPOST_X'])[1]

            # logger.info(f'X-VOA Setting (mA²): {setpoint_ma2}, TX Pwr (dBm): {txpwr_dbm}, PostPD_X ADC: {pdpost_adc}')

            TxPwrX_v_mw.append(txpwr_mw)
            PDPostX_v_adc.append(pdpost_adc)

        # 步骤4: 线性拟合与返回结果
        if not TxPwrX_v_mw or not PDPostX_v_adc:
            print("警告: 未收集到足够的X偏振PostPD数据进行拟合。")
            return 0.0, 0.0  # 返回默认值或错误指示

        PostPDX_Slope, PostPDX_Offset = linear_fit(TxPwrX_v_mw, PDPostX_v_adc)
        print(f"X偏振PostPD拟合结果: Slope = {PostPDX_Slope:.4e}, Offset = {PostPDX_Offset:.4f}")

        return PostPDX_Slope, PostPDX_Offset

    def post_cal_y_pol(self,
                       tof_dict_Y: dict,
                       VOAY_mA: float,
                       tof_dict_X: dict,
                       VOAX_Max: float,
                       VOAX_NULL_OFFSET: float,
                       VOAY_Max: float,
                       VOAY_NULL_OFFSET: float
                       ) -> tuple[float, float]:
        """执行TOFMPD（可调光滤波器监控光电二极管）后校准的Y偏振部分。

        此函数专注于校准Y偏振路径上的TOF监控光电二极管（PostPD）的响应。
        其逻辑与X偏振的校准函数 `_tofmpd_post_cal_x_pol` 对称。

        主要步骤:
        1.  **设置初始光路状态**:
            a.  将Y偏振的所有TOF (`PIC_TOFOUT1` 至 `PIC_TOFOUT7`) 设置到其已知的峰值DAC值（mA），
                这些值从 `tof_dict_Y` 中获取。
            b.  将Y偏振的VOA (`PIC_TOFOUTVOA`) 设置到其峰值DAC值 `VOAY_mA`。
            c.  为了最小化X偏振的串扰：
                i.  将X偏振的各个TOF (`PIC_TOFIN1` 至 `PIC_TOFIN7`) 设置到估算的零点附近
                    （通过在其mA值的平方 `values[1]` 上加一个小的偏移量 `3.25` mA²，然后开方）。
                ii. 将X偏振的VOA (`PIC_TOFINVOA`) 设置到其之前确定的零点位置，该位置由
                    其峰值平方 `VOAX_Max` 加上其零点偏移量 `VOAX_NULL_OFFSET` (mA²) 后开方得到。

        2.  **确定Y偏振VOA的扫描范围**:
            a.  计算Y偏振VOA的理论零点 (`Y_null_point_ma2`)，即其峰值平方 `VOAY_Max` 加上其零点偏移量 `VOAY_NULL_OFFSET` (mA²)。
            b.  定义扫描的起始点 (`start_range_ma2`) 为Y偏振VOA的峰值平方 `VOAY_Max`。
            c.  定义扫描步长 (`step_size_ma2`) 为Y偏振VOA零点偏移量 `VOAY_NULL_OFFSET` 的25%。
            d.  定义扫描的结束点 (`stop_range_ma2`) 为理论零点 `Y_null_point_ma2` 减去半个步长。
            e.  使用 `np.arange` 生成扫描点序列 `VOAY_PostPD_v_ma2` (单位mA²)。

        3.  **执行扫描并收集数据**:
            a.  初始化列表 `TxPwrY_v_mw` (mW) 和 `PDPostY_v_adc` (ADC读数)。
            b.  (假定光开关已正确设置，通常在X偏振校准后TX功率路径已打开)。
            c.  遍历 `VOAY_PostPD_v_ma2` 中的每个扫描点 (mA²):
                i.  转换并设置Y偏振VOA (`PIC_TOFOUTVOA`)。
                ii. 等待5秒。
                iii.读取发射光功率 (`txpwr_dbm`) 并转换为mW (`txpwr_mw`)。
                iv. 读取Y偏振PostPD的ADC值 (`pdpost_adc`)。
                v.  打印读数。
                vi. 存储 `txpwr_mw` 和 `pdpost_adc`。

        4.  **线性拟合与返回结果**:
            a.  调用 `linear_fit(TxPwrY_v_mw, PDPostY_v_adc)`。
            b.  返回拟合得到的斜率 (`PostPDY_Slope`) 和截距 (`PostPDY_Offset`)。

        Args:
            tof_dict_Y (dict): 包含Y偏振各TOF名称到其 (mA峰值, mA峰值平方) 元组映射的字典。
            VOAY_mA (float): Y偏振VOA (`PIC_TOFOUTVOA`) 的峰值DAC设置 (mA)。
            tof_dict_X (dict): 包含X偏振各TOF名称到其 (mA峰值, mA峰值平方) 元组映射的字典。
            VOAX_Max (float): X偏振VOA (`PIC_TOFINVOA`) 峰值DAC设置的平方 (mA²)。
            VOAX_NULL_OFFSET (float): X偏振VOA的零点偏移量 (mA²)，从EEPROM读取。
            VOAY_Max (float): Y偏振VOA (`PIC_TOFOUTVOA`) 峰值DAC设置的平方 (mA²)。
            VOAY_NULL_OFFSET (float): Y偏振VOA的零点偏移量 (mA²)，从EEPROM读取。

        Returns:
            tuple[float, float]: 一个元组，包含：
                - `PostPDY_Slope` (float): Y偏振PostPD响应的线性拟合斜率。
                - `PostPDY_Offset` (float): Y偏振PostPD响应的线性拟合截距。
        """
        # 步骤1: 设置初始光路状态
        # 设置Y偏振TOF和VOA到峰值
        for key, values in tof_dict_Y.items():
            self.cim8.excecute_mcm_and_disp_result('SET', 'BSP_DAC_REG', [1, key, values[0]])
        self.cim8.excecute_mcm_and_disp_result('SET', 'BSP_DAC_REG', [1, 'PIC_TOFOUTVOA', VOAY_mA])

        # 设置X偏振TOF到估算零点，X-VOA到其零点偏移位置，以最小化串扰
        for key, values in tof_dict_X.items():
            estimated_x_tof_null_mA = math.sqrt(values[1] + 3.25)
            self.cim8.excecute_mcm_and_disp_result('SET', 'BSP_DAC_REG', [1, key, estimated_x_tof_null_mA])

        VOAX_NULL_mA = math.sqrt(VOAX_Max + VOAX_NULL_OFFSET)
        self.cim8.excecute_mcm_and_disp_result('SET', 'BSP_DAC_REG', [1, 'PIC_TOFINVOA', VOAX_NULL_mA])

        # 步骤2: 确定Y偏振VOA的扫描范围
        Y_null_point_ma2: float = VOAY_Max + VOAY_NULL_OFFSET  # Y-VOA理论零点 (mA²)

        start_range_ma2: float = VOAY_Max  # 扫描起始点为Y-VOA峰值 (mA²)
        # 使用VOAY_NULL_OFFSET计算Y偏振的步长
        step_size_ma2: float = 0.25 * VOAY_NULL_OFFSET
        stop_range_ma2: float = Y_null_point_ma2 - step_size_ma2 * 0.5

        VOAY_PostPD_v_ma2: np.ndarray = np.arange(round(start_range_ma2, 4), stop_range_ma2, step_size_ma2)

        # 步骤3: 执行扫描并收集数据
        TxPwrY_v_mw: list[float] = []
        PDPostY_v_adc: list[int] = []

        logger.info("开始扫描Y偏振VOA并收集PostPD数据...")
        for setpoint_ma2 in VOAY_PostPD_v_ma2:
            current_voay_setting_mA = math.sqrt(setpoint_ma2)
            self.cim8.excecute_mcm_and_disp_result('SET', 'BSP_DAC_REG', [1, 'PIC_TOFOUTVOA', current_voay_setting_mA])
            time.sleep(5)

            txpwr_dbm: float = self.get_tx_power(self.cim8.freq)
            txpwr_mw: float = 10 ** (txpwr_dbm / 10)
            pdpost_adc: int = self.cim8.excecute_mcm_and_disp_result('get', 'BSP_ADC_REG', [1, 'PIC_TOFPDPOST_Y'])[1]

            # logger.info(f'Y-VOA Setting (mA²): {setpoint_ma2}, TX Pwr (dBm): {txpwr_dbm}, PostPD_Y ADC: {pdpost_adc}')

            TxPwrY_v_mw.append(txpwr_mw)
            PDPostY_v_adc.append(pdpost_adc)

        # 步骤4: 线性拟合与返回结果
        if not TxPwrY_v_mw or not PDPostY_v_adc:
            logger.warning("警告: 未收集到足够的Y偏振PostPD数据进行拟合。")
            return 0.0, 0.0

        PostPDY_Slope, PostPDY_Offset = linear_fit(TxPwrY_v_mw, PDPostY_v_adc)
        logger.info(f"Y偏振PostPD拟合结果: Slope = {PostPDY_Slope:.4e}, Offset = {PostPDY_Offset:.4f}")

        return PostPDY_Slope, PostPDY_Offset

    def post_cal_restore_and_save(self,
                                  tof_dict_X: dict,
                                  tof_dict_Y: dict,
                                  VOAX_mA: float,
                                  VOAY_mA: float,
                                  PostPDX_Slope: float,
                                  PostPDX_Offset: float,
                                  PostPDY_Slope: float,
                                  PostPDY_Offset: float,
                                  Freq_REF: float,
                                  TEMP_REF: float
                                  ) -> None:
        """恢复TOF、VOA设置，启用控制环路，保存TOFMPD后校准结果至EEPROM并复位模块。

        在TOFMPD（可调光滤波器监控光电二极管）后校准的X偏振和Y偏振扫描完成后，
        此函数负责执行以下关键的收尾操作：

        1.  **恢复TOF和VOA至峰值设置**:
            -   将X偏振的所有TOF (`PIC_TOFIN1` 至 `PIC_TOFIN7`) DAC恢复到其各自的峰值设置
                （从 `tof_dict_X` 中获取mA值）。
            -   将Y偏振的所有TOF (`PIC_TOFOUT1` 至 `PIC_TOFOUT7`) DAC恢复到其各自的峰值设置
                （从 `tof_dict_Y` 中获取mA值）。
            -   将X偏振VOA (`PIC_TOFINVOA`) DAC恢复到其峰值设置 `VOAX_mA`。
            -   将Y偏振VOA (`PIC_TOFOUTVOA`) DAC恢复到其峰值设置 `VOAY_mA`。
            这些峰值通常是在之前的TOF/VOA校准阶段确定的。

        2.  **恢复模块控制**:
            -   调用 `_set_tof_voa_control_loops(enable=True)` 重新启用TOF/VOA的用户控制环路，
                使模块恢复到闭环控制状态。

        3.  **保存校准系数到EEPROM**:
            将TOFMPD后校准过程中计算得到的以下参数写入模块的EEPROM：
            -   X偏振PostPD的斜率和偏移量 (`PostPDX_Slope`, `PostPDX_Offset`)，
                保存到 'PIC_TOF_PDPOST_X_COEFF'。
            -   Y偏振PostPD的斜率和偏移量 (`PostPDY_Slope`, `PostPDY_Offset`)，
                保存到 'PIC_TOF_PDPOST_Y_COEFF'。
            -   校准时的参考频率 (`Freq_REF`)，保存到 'PIC_TOF_PDPOST_F_REF'。
            -   校准时的参考PIC温度 (`TEMP_REF`)，保存到 'PIC_TOF_PDPOST_T_REF'。

        4.  **模块复位**:
            -   调用 `_reset_module_and_wait()` 对模块进行软复位，
                确保新的校准数据完全生效，并等待模块重新达到就绪状态。

        Args:
            tof_dict_X (dict): 包含X偏振各TOF名称到其 (mA峰值, mA峰值平方) 元组映射的字典。
                               `values[0]` 即为mA峰值。
            tof_dict_Y (dict): 包含Y偏振各TOF名称到其 (mA峰值, mA峰值平方) 元组映射的字典。
                               `values[0]` 即为mA峰值。
            VOAX_mA (float): X偏振VOA (`PIC_TOFINVOA`) 的峰值DAC设置 (mA)。
            VOAY_mA (float): Y偏振VOA (`PIC_TOFOUTVOA`) 的峰值DAC设置 (mA)。
            PostPDX_Slope (float): X偏振PostPD响应的线性拟合斜率。
            PostPDX_Offset (float): X偏振PostPD响应的线性拟合截距。
            PostPDY_Slope (float): Y偏振PostPD响应的线性拟合斜率。
            PostPDY_Offset (float): Y偏振PostPD响应的线性拟合截距。
            Freq_REF (float): 校准时的参考频率 (THz)。
            TEMP_REF (float): 校准时的参考PIC（光子集成电路）温度。

        Returns:
            None
        """
        # 恢复tof&voa到peak点
        for key, values in tof_dict_X.items():
            self.cim8.excecute_mcm_and_disp_result('SET', 'BSP_DAC_REG', [1, key, values[0]])
        for key, values in tof_dict_Y.items():
            self.cim8.excecute_mcm_and_disp_result('SET', 'BSP_DAC_REG', [1, key, values[0]])
        self.cim8.excecute_mcm_and_disp_result('SET', 'BSP_DAC_REG', [1, 'PIC_TOFINVOA', VOAX_mA])
        self.cim8.excecute_mcm_and_disp_result('SET', 'BSP_DAC_REG', [1, 'PIC_TOFOUTVOA', VOAY_mA])

        # 恢复模块控制
        self.set_tof_voa_control_loops(True)
        # 保存到EEPROM
        self.cim8.excecute_mcm_and_disp_result('ESET', 'PIC_TOF_PDPOST_X_COEFF', [1, PostPDX_Slope, PostPDX_Offset])
        self.cim8.excecute_mcm_and_disp_result('ESET', 'PIC_TOF_PDPOST_Y_COEFF', [1, PostPDY_Slope, PostPDY_Offset])
        self.cim8.excecute_mcm_and_disp_result('ESET', 'PIC_TOF_PDPOST_F_REF', [1, Freq_REF])
        self.cim8.excecute_mcm_and_disp_result('ESET', 'PIC_TOF_PDPOST_T_REF', [1, TEMP_REF])

        self.reset_module_and_wait()

    def get_iq_imbalance_values(self) -> Tuple[float, float]:
        """获取IQ Imbalance的值。

        Returns:
            (IQ_imb_meas_H_dB, IQ_imb_meas_V_dB)的元组
        """
        IQ_reg_X = self.cim8.get_regs(Ax1200PcieDefs.NTWK_RX_IQ_GAIN_IMBAL_Y_POL_MON, 1)[0]
        IQ_imb_meas_H_dB = signed_num(IQ_reg_X, 16)
        IQ_reg_Y = self.cim8.get_regs(Ax1200PcieDefs.NTWK_RX_IQ_GAIN_IMBAL_X_POL_MON, 1)[0]
        IQ_imb_meas_V_dB = signed_num(IQ_reg_Y, 16)
        return IQ_imb_meas_H_dB * 0.001, IQ_imb_meas_V_dB * 0.001

    def tx_iq_imbalance_calibration_loop(self, IQ_imb_target_dB: float, delay: float) -> Tuple[float, float, float, float]:
        """TX IQ Imbalance 校准循环。

        Args:
            IQ_imb_target_dB: 目标IQ不平衡值（dB）
            delay: 延迟时间

        Returns:
            (HI_IQ_imb_curr, HQ_IQ_imb_curr, VI_IQ_imb_curr, VQ_IQ_imb_curr)的元组
        """
        IQ_imb_meas_H_dB, IQ_imb_meas_V_dB = self.get_iq_imbalance_values()
        loop_times = 0
        while abs(IQ_imb_meas_H_dB) > IQ_imb_target_dB or abs(IQ_imb_meas_V_dB) > IQ_imb_target_dB:
            loop_times += 1
            logger.info('开始进行第%d次IQ Balance调节' % loop_times)
            # calculate correction
            HI_IQ_corr = 10 ** (IQ_imb_meas_H_dB / 20)
            HQ_IQ_corr = 1 / HI_IQ_corr
            VI_IQ_corr = 10 ** (IQ_imb_meas_V_dB / 20)
            VQ_IQ_corr = 1 / VI_IQ_corr
            HI_IQ_imb_curr, HQ_IQ_imb_curr, VI_IQ_imb_curr, VQ_IQ_imb_curr = \
                self.cim8.excecute_mcm_and_disp_result('GET', 'PIC_MDL_IQ_IMB_PDO_CAL', [1])
            # 计算correction
            HI_IQ_imb_new = HI_IQ_imb_curr * HI_IQ_corr
            HQ_IQ_imb_new = HQ_IQ_imb_curr * HQ_IQ_corr
            VI_IQ_imb_new = VI_IQ_imb_curr * VI_IQ_corr
            VQ_IQ_imb_new = VQ_IQ_imb_curr * VQ_IQ_corr
            self.cim8.excecute_mcm_and_disp_result("set", "PIC_MDL_IQ_IMB_PDO_CAL",
                                                   [1, HI_IQ_imb_new, HQ_IQ_imb_new, VI_IQ_imb_new,
                                                    VQ_IQ_imb_new])  # Configure MDL IQ IMB threshold
            time.sleep(delay)
            IQ_imb_meas_H_dB, IQ_imb_meas_V_dB = self.get_iq_imbalance_values()
            logger.info(f'IQ_imb_meas_H_dB:{IQ_imb_meas_H_dB}   IQ_imb_meas_V_dB:{IQ_imb_meas_V_dB}')
        return self.cim8.excecute_mcm_and_disp_result('GET', 'PIC_MDL_IQ_IMB_PDO_CAL', [1])

    def tx_iq_imbalance_save_and_cleanup(self, cal_values: Tuple[float, float, float, float]) -> None:
        """保存校准结果并清理。

        Args:
            cal_values: 校准值元组(HI_IQ_imb_curr, HQ_IQ_imb_curr, VI_IQ_imb_curr, VQ_IQ_imb_curr)
        """
        HI_IQ_imb_curr, HQ_IQ_imb_curr, VI_IQ_imb_curr, VQ_IQ_imb_curr = cal_values
        self.cim8.excecute_mcm_and_disp_result('ESET', 'PIC_MDL_IQ_IMB_PDO_CAL',
                                               [1, HI_IQ_imb_curr, HQ_IQ_imb_curr, VI_IQ_imb_curr, VQ_IQ_imb_curr])
        print('TX IQ imbalance 校准结束\n\n')
        print('断开扰偏仪')
        self.osw_sop.switch_channel(self.osw_sop_off)

    def set_fpga_regs(self) -> None:
        """修改FPGA寄存器值以进行PIC TXPDs相关配置。"""
        self.cim8.excecute_mcm_and_disp_result('SET', 'FPGA_REG', [1, 0xF05C, 0x03E8])
        self.cim8.excecute_mcm_and_disp_result('SET', 'FPGA_REG', [1, 0xF060, 0x03E8])
        self.cim8.excecute_mcm_and_disp_result('SET', 'FPGA_REG', [1, 0xF07C, 0x03E8])
        self.cim8.excecute_mcm_and_disp_result('SET', 'FPGA_REG', [1, 0xF080, 0x03E8])
        self.cim8.excecute_mcm_and_disp_result('SET', 'FPGA_REG', [1, 0xF064, 0x01F4])
        self.cim8.excecute_mcm_and_disp_result('SET', 'FPGA_REG', [1, 0xF084, 0x01F4])

    def collect_and_save_spectrum_data(self, InitPICTemp: float, timestamp_str: str) -> str:
        """收集光谱数据并根据file_label保存到CSV。

        Args:
            InitPICTemp: 初始PIC温度
            timestamp_str: 时间戳字符串

        Returns:
            保存的CSV文件路径
        """

        # 将时间插入csv_filepath
        csv_filepath = f'{self.cim8.SN}_TX_Spectrum_Cal_Initial_TC_20C_TPIC_{InitPICTemp}C_{timestamp_str}.csv'
        num_traces = 10
        wavelen_data_list, pwr_data_list = [], []
        while num_traces:
            self.osa.setup(1548.316, 1550.316, 0.02)
            wavelen, pwr = self.osa.get_spectra_cim8()
            wavelen_data_list.append(wavelen)
            pwr_data_list.append(pwr)
            num_traces -= 1
            time.sleep(1)

        Frequency_THz = np.mean(wavelen_data_list, axis=0)
        Power_dBm = np.mean(pwr_data_list, axis=0)

        spectrum_data = {
            'Frequency(THz)': Frequency_THz,
            # Ensure p_val is positive for log
            'Power(dBm)': [10 * math.log10(p_val) for p_val in Power_dBm]

        }
        df_spectrum = pd.DataFrame(spectrum_data)
        # Assuming CalTemp is consistently 20C for these filenames as per original naming
        csv_filepath = os.path.join(self.cal_result_path, csv_filepath)
        df_spectrum.to_csv(csv_filepath, index=False, header=False)
        return csv_filepath

    def _run_calibration_executable(self, executable_name: str, arguments: list, expected_log_file: str,
                                    expected_pass_string: str = "Calibration PASS") -> bool:
        """运行外部校准可执行文件并验证其输出。

        此函数会执行指定的外部校准程序，并监控其生成的日志文件以判断校准是否成功。
        它会首先切换到可执行文件所在的目录 (`exe_path`，一个全局变量)，
        然后执行程序。执行后，它会等待预期的日志文件 (`expected_log_file`) 生成，
        并检查日志文件的最后一行是否包含预期的成功标识字符串 (`expected_pass_string`)。

        Args:
            executable_name (str): 可执行文件的名称。
                例如：'calibration_tx_spectrum_from_file.exe'。
            arguments (list): 传递给可执行文件的参数列表。
            expected_log_file (str): 预期生成的日志文件名，用于检查校准结果。
                例如：'Tx_spectrum_calibration_log_.txt'。
            expected_pass_string (str, optional): 日志文件中表示校准成功的字符串。
                默认为 "Calibration PASS"。

        Returns:
            bool: 如果可执行文件成功运行并且日志表明校准通过，则返回 True；
                  否则返回 False。在发生错误或超时的情况下，会提示用户手动检查。

        Raises:
            FileNotFoundError: 如果可执行文件或预期的日志文件未找到（尽管函数内部会捕获此异常并返回False）。
            subprocess.CalledProcessError: 如果可执行文件以非零退出码结束（函数内部捕获并返回False）。
            Exception: 捕获其他在执行过程中发生的未知异常，并返回False。
        """
        original_cwd = os.getcwd()  # 保存当前工作目录，以便后续恢复
        try:
            os.chdir(self.exe_path)  # 切换到可执行文件所在的目录
            logger.info(f"正在运行可执行文件: {executable_name}，参数: {arguments}")

            # 推荐使用 shell=False 以增强安全性与跨平台兼容性
            command_to_run = [executable_name] + arguments
            subprocess.run(command_to_run, shell=False, check=True)  # check=True 会在返回非零退出码时抛出 CalledProcessError

            log_file_path = os.path.join(self.cal_result_path, expected_log_file)  # self.cal_result_path 是全局定义的日志目录

            # 等待日志文件生成，设置超时时间
            timeout_seconds = 30
            start_time = time.time()
            log_found = False
            while time.time() - start_time <= timeout_seconds:
                if os.path.exists(log_file_path):
                    log_found = True
                    break
                time.sleep(1)  # 每秒检查一次

            if not log_found:
                logger.error(f"超时错误：日志文件 {log_file_path} 在 {timeout_seconds} 秒后未找到。")
                return False

            # 读取日志文件的最后一行
            with open(log_file_path, 'r', encoding='gbk') as f:  # 指定UTF-8编码以兼容不同日志内容
                lines = f.read().splitlines()
                last_line = lines[-1] if lines else ""

            logger.info(f"日志文件最后一行内容: {last_line}")
            if expected_pass_string not in last_line:
                logger.error(f'可执行文件 {executable_name} 未报告成功。日志最后一行: "{last_line}"。请手动检查。')
                return False

            logger.info(f"可执行文件 {executable_name} 成功完成。")
            return True

        except FileNotFoundError:
            logger.error(
                f"文件未找到错误：可执行文件 {executable_name} 在 {os.path.join(self.exe_path, executable_name)} "
                f"未找到，或日志文件 {expected_log_file} 未找到。")
            logger.error(f"可执行文件 {executable_name} 或其日志文件未找到。请手动检查。")
            return False
        except subprocess.CalledProcessError as e:
            logger.error(f"执行错误：可执行文件 {executable_name} 返回非零退出码 {e.returncode}。")
            logger.error(f"可执行文件 {executable_name} 执行失败。请手动检查。")
            return False
        except Exception as e:
            logger.error(f"运行 {executable_name} 时发生意外错误: {e}")
            logger.error(f"运行 {executable_name} 时发生意外错误。请手动检查。")
            return False
        finally:
            os.chdir(original_cwd)  # 无论执行结果如何，都恢复原始工作目录

    def get_Jannu_ADC_captrue_file_name(self, time_str: str) -> str:
        """获取Jannu ADC捕获文件名。

        Args:
            time_str: 时间戳字符串

        Returns:
            ADC数据文件的完整路径
        """
        # 添加可执行文件执行成功后结果
        Jannu_ADC_captrue_file_name = os.path.join(self.cal_result_path, f'ADC_data_{self.cim8.SN}_{time_str}')
        self.append_result_file_list(Jannu_ADC_captrue_file_name)
        return Jannu_ADC_captrue_file_name

    def get_Jannu_FRONT_end_file_name(self, Jannu_type: str, time_str: str) -> str:
        """获取Jannu前端文件名。

        Args:
            Jannu_type: Jannu类型
            time_str: 时间戳字符串

        Returns:
            前端设置文件的完整路径
        """
        Jannu_FRONT_end_file_name = os.path.join(self.cal_result_path,
                                                 f'{Jannu_type}_settings_{self.cim8.SN}_{time_str}.txt')
        self.append_result_file_list(Jannu_FRONT_end_file_name)
        return Jannu_FRONT_end_file_name

    def tx_spectrum_cal_run_executable(self, settings_filename: str, data_filepath: str) -> bool:
        """运行外部TX Spectrum校准程序。

        Args:
            settings_filename: 设置文件名
            data_filepath: 数据文件路径

        Returns:
            执行是否成功
        """
        executable = 'calibration_tx_spectrum_from_file.exe'
        args = ['-t', settings_filename,
                '-o', data_filepath,
                '-f', self.cal_result_path,
                '-l', '193500']
        log_file = 'Tx_spectrum_calibration_log_.txt'
        # 如果执行失败，_run_calibration_executable 内部会处理 input() 提示
        return self._run_calibration_executable(executable, args, log_file)

    def tx_spectrum_cal_process_results(self, timestamp_str: str) -> None:
        """处理校准结果文件并设置到模块。

        Args:
            timestamp_str: 时间戳字符串
        """
        original_file = self.cal_result_path + '\\tx_settings_after_cal_.txt'
        with open(original_file, "r", encoding="utf-8") as f:
            content = f.read()
        after_settings_filename = self.cal_result_path + f'\\tx_settings_after_cal_{self.cim8.SN}_{timestamp_str}.txt'
        with open(after_settings_filename, "w", encoding="utf-8") as f:
            f.write(content)
        os.remove(original_file)
        result = self.cim8.Jannu_TX_FRONT_end('set', after_settings_filename)
        if result:
            self.cim8.handleDiagSetGetCmd('set', 'TMS_MISC_CMD', [1, 1])  # Load the new settings to module eeprom
            self.cim8.handleDiagSetGetCmd('set', 'TMS_MISC_CMD', [1, 3])  # Save the change to module eeprom
            return True
        else:
            logger.error('TX spectrum配置失败请检查')
            return False

    def tx_rx_frontend_cal_run_executable(self, adc_data_filename: str, tx_settings_filename: str,
                                         rx_settings_filename: str) -> bool:
        """运行外部TX/RX Frontend校准程序。

        Args:
            adc_data_filename: ADC数据文件名
            tx_settings_filename: TX设置文件名
            rx_settings_filename: RX设置文件名

        Returns:
            执行是否成功
        """
        executable = 'calibration_tx_rx_frontend_from_file.exe'
        args = ['-a', adc_data_filename,
                '-t', tx_settings_filename,
                '-r', rx_settings_filename,
                '-f', self.cal_result_path]
        # 假设此可执行文件也使用 'Tx_spectrum_calibration_log_.txt'。
        log_file = 'Tx_spectrum_calibration_log_.txt'
        # 如果执行失败，_run_calibration_executable 内部会处理 input() 提示
        self._run_calibration_executable(executable, args, log_file)

    def tx_rx_frontend_cal_process_setting_file(self, original_suffix: str, new_prefix: str,
                                               timestamp_str: str) -> str:
        """处理校准后的设置文件（添加时间戳并重命名）。

        Args:
            original_suffix: 原始文件后缀
            new_prefix: 新文件前缀
            timestamp_str: 时间戳字符串

        Returns:
            新文件的完整路径
        """
        original_file = self.cal_result_path + f'\\{original_suffix}'
        with open(original_file, "r", encoding="utf-8") as f:
            content = f.read()
        new_filename = f'{new_prefix}_{self.cim8.SN}_{timestamp_str}.txt'
        new_file_path = self.cal_result_path + f'\\{new_filename}'
        self.append_result_file_list(new_file_path)
        with open(new_file_path, "w", encoding="utf-8") as f:
            f.write(content)
        os.remove(original_file)
        return new_file_path

    def tx_rx_frontend_cal_apply_settings_and_reset(self, tx_settings_file: str, rx_settings_file: str) -> None:
        """应用校准后的设置并重启模块。

        Args:
            tx_settings_file: TX设置文件路径
            rx_settings_file: RX设置文件路径
        """
        self.cim8.Jannu_TX_FRONT_end('set', tx_settings_file)
        self.cim8.Jannu_RX_FRONT_end('set', rx_settings_file)
        time.sleep(5)
        self.cim8.handleDiagSetGetCmd('set', 'TMS_MISC_CMD', [1, 1])
        self.cim8.handleDiagSetGetCmd('set', 'QEF_MISC_CMD', [1, 1])
        self.cim8.handleDiagSetGetCmd('set', 'TMS_MISC_CMD', [1, 3])
        self.cim8.handleDiagSetGetCmd('set', 'QEF_MISC_CMD', [1, 3])
        self.cim8.SoftmodRst()

    def pump_ratio_cal_initial_coeffs_check_and_set(self) -> None:
        """检查并设置初始PUMP LASER系数。"""
        data = self.cim8.handleDiagSetGetCmd('eget', 'PUMP_LAS_COEFFS', [1])
        strs = data.split('\n\n-PASS-')[0].split('\n')
        coeffs: list = [float(x) for x in strs]
        reset_and_waited_in_if = False
        if coeffs[-5] != 1.0:
            coeffs[-5] = 1.0
            self.cim8.handleDiagSetGetCmd('eset', 'PUMP_LAS_COEFFS', [1] + coeffs)
            self.cim8.handleDiagSetGetCmd('eset', 'PUMP_LAS_EEPROM_HDR', [1, 0xCAFEBABE])
            self.reset_module_and_wait(sleep_duration=5, ensure_ready_after_normal_power=True)
            reset_and_waited_in_if = True

        if not reset_and_waited_in_if:
            self.cim8.waite_for_module_ready()

    def pump_ratio_cal_setup_and_get_refs(self) -> Tuple[float, float, float]:
        """设置OSW，获取初始PUMP_ADJ，设置TX功率并获取参考温度和频率。

        Returns:
            (pump_adj_init, TEMP_REF, FREQ_REF)的元组
        """
        self.osw_tr.switch_channel(self.osw_tr_tx_pow)
        pump_adj_init, _ = self.cim8.excecute_mcm_and_disp_result('get', 'BSP_DAC_REG', [1, 'PUMP0_PWR_ADJ'])
        self._configure_tx_freq_power(power=5)
        TEMP_REF = self.cim8.get_pic_temp()
        FREQ_REF = self.cim8.get_freq()
        time.sleep(3)
        return pump_adj_init, TEMP_REF, FREQ_REF

    def _search_edfa_current_target(self, pwr_target: float) -> Tuple[float, float]:
        """搜索EDFA电流目标以达到期望的功率输出。

        Args:
            pwr_target: 目标功率输出（dBm）。

        Returns:
            (泵浦调整值, 实际功率)的元组。
        """
        pump_step = 5
        iteration_count = 0
        power_tolerance = 0.3
        max_iterations = 1000

        for _ in range(max_iterations):
            pump_adj, _ = self.cim8.excecute_mcm_and_disp_result(
                'get', 'BSP_DAC_REG', [1, 'PUMP0_PWR_ADJ']
            )
            actual_power = self.get_tx_power(self.cim8.freq)

            logger.info(f'第{iteration_count}次迭代: PUMP_ADJ = {pump_adj}, 功率 = {actual_power} dBm')

            if abs(actual_power - pwr_target) < power_tolerance:
                return pump_adj, actual_power
            elif actual_power < pwr_target - power_tolerance:
                pump_adj += pump_step
            else:
                pump_adj -= pump_step

            self.cim8.excecute_mcm_and_disp_result('SET', 'BSP_DAC_REG', [1, 'PUMP0_PWR_ADJ', pump_adj])
            time.sleep(1)
            iteration_count += 1

        logger.warning(f'EDFA电流搜索在{max_iterations}次迭代后未收敛')
        return pump_adj, actual_power

    def pump_ratio_cal_calculate_and_set_ratio(
        self,
        pump_adj_init: float,
        temp_ref: float,
        freq_ref: float,
        outpwr_max: float
    ) -> None:
        """计算PUMP比例并保存到EEPROM。

        Args:
            pump_adj_init: 初始泵浦调整值。
            temp_ref: 参考温度。
            freq_ref: 参考频率。
            outpwr_max: 最大输出功率目标。
        """
        current_target, _ = self._search_edfa_current_target(outpwr_max)
        ratio_pump = current_target / pump_adj_init

        # 获取当前系数
        data = self.cim8.handleDiagSetGetCmd('eget', 'PUMP_LAS_COEFFS', [1])
        coefficient_strings = data.split('\n\n-PASS-')[0].split('\n')
        coefficients = [float(x) for x in coefficient_strings]

        # 更新比例系数
        coefficients[-5] = ratio_pump

        # 保存更新的系数和参考值
        self.cim8.handleDiagSetGetCmd('eset', 'PUMP_LAS_COEFFS', [1] + coefficients)
        self.cim8.handleDiagSetGetCmd('eset', 'PUMP_LAS_REFS', [1, temp_ref, freq_ref])
        self.cim8.handleDiagSetGetCmd('eset', 'PUMP_LAS_EEPROM_HDR', [1, EEPROM_HEADER_VALUE])

        self.reset_module_and_wait(sleep_duration=0, ensure_ready_after_normal_power=False)

    def tx_pdl_cal_initial_setup(self) -> None:
        """TX PDL 校准的初始设置，包括仪器设置和BER范围调整。"""
        logger.info('TX PDL 校准开始')
        self.cim8.set_tx_pow(0)
        self.set_sop_in()
        att_min_val, att_max_val = 0, self.voa_ase.il_max
        self.voa_ase.set_attr(att_max_val)
        self.set_rx_power(-6, 0)  # Sleep/wait is handled by subsequent logic
        self.osw_ase.switch_channel(self.osw_ase_on)
        demod_lock_elapsed_time = self.cim8.get_time_DemodLock()
        if demod_lock_elapsed_time < 60:
            time.sleep(65 - demod_lock_elapsed_time)

        ber_pdl_range_const = [1.1e-2, 1.3e-2]
        current_ber = self.cim8.get_pre_BER()
        loop_count = 20
        while loop_count:
            loop_count -= 1
            if ber_pdl_range_const[0] < current_ber < ber_pdl_range_const[1]:
                break
            else:
                att_val = self.voa_ase.set_attr((att_min_val + att_max_val) / 2)
                time.sleep(3)
                current_ber = self.cim8.get_pre_BER()
                logger.info(f'调试total_inpower={self.cim8.get_total_inpower()}\tatt={att_val}\tber={current_ber}')
                if current_ber > ber_pdl_range_const[1]:
                    att_min_val = att_val
                elif current_ber < ber_pdl_range_const[0]:
                    att_max_val = att_val
        self.cim8.reset_counter()
        if self.cim8.get_uncorr_blks_cnt_acc():
            logger.error(f'BER = {self.cim8.get_pre_BER()}时仍有误码，请确认！')
        return ber_pdl_range_const  # Though not used later, kept for consistency if needed

    def tx_pdl_cal_get_initial_values(self) -> Tuple[float, float, int, int]:
        """获取TX PDL校准的初始DAC、缩放和ADC值。

        Returns:
            (dac值, 缩放值, adc_x值, adc_y值)的元组。
        """
        _, dac_val = self.cim8.excecute_mcm_and_disp_result("get", "BSP_DAC_REG", [1, 'TXPOWXY'])
        scale_val, offset_val = self.cim8.excecute_mcm_and_disp_result(
            'eget', 'BSP_DAC_EEPROM_SCALING', [1, 'TXPOWXY']
        )
        scaling_txpowxy_val = (scale_val + offset_val) / 1  # 原始逻辑

        _, adc_value_x_val = self.cim8.excecute_mcm_and_disp_result(
            "get", "BSP_ADC_REG", [1, 'PIC_PMON_TXPDX']
        )
        _, adc_value_y_val = self.cim8.excecute_mcm_and_disp_result(
            "get", "BSP_ADC_REG", [1, 'PIC_PMON_TXPDY']
        )

        return dac_val, scaling_txpowxy_val, adc_value_x_val, adc_value_y_val

    @staticmethod
    def _ber_to_q(ber_value: float) -> float:
        """将BER值转换为Q因子。

        Args:
            ber_value: 误码率值。

        Returns:
            Q因子值。
        """
        if ber_value <= 0 or ber_value >= 0.5:  # erfcinv在(0, 2)范围外未定义
            # 适当处理边界情况或无效BER值
            # 例如，返回非常低或非常高的Q值，或抛出错误
            print(f"警告: BER值 {ber_value} 超出Q转换的有效范围。")
            return -float('inf') if ber_value >= 0.5 else float('inf')  # 占位符，根据需要调整
        q_factor = 20 * math.log10(2 ** 0.5 * erfcinv(2 * ber_value))
        return q_factor

    def get_delta_q(self) -> float:
        """获取X和Y偏振的BER，计算Q因子，并返回它们的差值dQ。

        Returns:
            X和Y偏振Q因子的差值。
        """
        ber_x = self.cim8.get_ber_x()
        ber_y = self.cim8.get_ber_y()
        q_ber_x = self._ber_to_q(ber_x)
        q_ber_y = self._ber_to_q(ber_y)
        delta_q_val = q_ber_x - q_ber_y
        return delta_q_val

    def set_pdl_control_loops(self, enable: bool) -> None:
        """启用或禁用TX PDL相关的控制环路。

        Args:
            enable: True启用控制环路，False禁用控制环路。
        """
        val_switch = 1 if enable else 0
        val_fpga = 0x0cc3 if enable else 0
        self.cim8.excecute_mcm_and_disp_result('set', 'PIC_SW_CNTL_TXPOWXY', [1, val_switch])
        self.cim8.excecute_mcm_and_disp_result('set', 'PIC_SW_CNTL_MDL_OPT', [1, val_switch])
        self.cim8.excecute_mcm_and_disp_result('set', 'FPGA_REG', [1, 0Xf0e8, val_fpga])

    def tx_pdl_cal_iteration_loop(self, initial_dq_val, initial_dac_value, scaling_txpowxy_val, pdl_step_val,
                                  tx_pdl_q_error_val, index_max_val, dac_txpowxy_limits_val):
        """TX PDL 校准迭代循环"""
        tx_pdl_q_error_val = float(tx_pdl_q_error_val)
        dac_txpowxy_limits_val = list(dac_txpowxy_limits_val)
        current_dq = initial_dq_val
        current_dac_value = initial_dac_value
        pdl_step_to_use = pdl_step_val
        iteration_index = 0

        while abs(current_dq) > tx_pdl_q_error_val:
            if iteration_index > index_max_val:
                return 1, 'PDL校准失败，尝试次数超出限制', current_dac_value, current_dq  # Return current DAC and dQ
            else:
                current_dac_value += pdl_step_to_use
                scaling_ma_val = current_dac_value * scaling_txpowxy_val
                if not (dac_txpowxy_limits_val[0] <= current_dac_value <= dac_txpowxy_limits_val[1]):
                    return 1, 'PDL 校准失败，DAC值超出门限', current_dac_value, current_dq

                res_set_dac = self.cim8.handleDiagSetGetCmd("set", "BSP_DAC_REG", [1, 'TXPOWXY', scaling_ma_val])
                if 'PASS' not in res_set_dac:
                    return 1, f'TXPOWXY 设置为{scaling_ma_val} 失败', current_dac_value, current_dq
                logger.info(f'TXPOWXY 设置为{scaling_ma_val} 成功')

                _, current_dac_value_read = self.cim8.excecute_mcm_and_disp_result("get", "BSP_DAC_REG", [1, 'TXPOWXY'])
                # SCALING_TXPOWXY might need re-evaluation if it's dynamic, but original code doesn't do this in loop
                # Using the initial scaling
                logger.info(
                    f'DAC_VALUE: {current_dac_value_read}, SCALING_TXPOWXY: {scaling_txpowxy_val}')
                current_dac_value = current_dac_value_read  # Update DAC value with read one

                self.cim8.reset_counter()
                time.sleep(5)
                current_dq = self.get_delta_q()
                logger.info(f'dQ: {current_dq}')

                if iteration_index == 0 and abs(current_dq) > abs(initial_dq_val):
                    logger.error('调节方向错误，变更调整方向！')
                    pdl_step_to_use = -pdl_step_to_use
                iteration_index += 1
        return 0, "PDL校准成功", current_dac_value, current_dq  # Success

    def tx_pdl_cal_save_results(self, final_dq: float, tx_pdl_q_error_val: float,
                               adc_value_x_val: int, adc_value_y_val: int) -> None:
        """保存TX PDL校准结果到EEPROM。

        Args:
            final_dq: 最终的dQ值
            tx_pdl_q_error_val: TX PDL Q误差值
            adc_value_x_val: X偏振ADC值
            adc_value_y_val: Y偏振ADC值
        """
        tx_pdl_q_error_val = float(tx_pdl_q_error_val)

        if final_dq < tx_pdl_q_error_val:  # Original condition was dQ < TX_PDL_Q_ERROR
            txpowxy_ma_val = self.cim8.BSP_DAC_REG('TXPOWXY')
            txpd_ratio_val = adc_value_x_val / adc_value_y_val if adc_value_y_val != 0 else float(
                'inf')  # Avoid division by zero
            txpowxy_init_val = txpowxy_ma_val
            tx_pdl_therm_ref_val = self.cim8.get_pic_temp()
            tx_pdl_freq_ref_val = self.cim8.get_freq()

            self.cim8.excecute_mcm_and_disp_result('ESET', 'PIC_TXPOWXY_PDL_COEFFS', [1, 0, 0, 0, txpd_ratio_val])
            self.cim8.excecute_mcm_and_disp_result('ESET', 'PIC_TXPOWXY_PDL_REF',
                                                   [1, tx_pdl_therm_ref_val, tx_pdl_freq_ref_val])
            self.cim8.excecute_mcm_and_disp_result('ESET', 'PIC_TXPOWXY_INIT', [1, txpowxy_init_val])
            self.cim8.excecute_mcm_and_disp_result('ESET', 'PIC_EEPROM_HDR', [1, 0xCAFEBABE])
        else:
            print(
                f"PDL calibration finished with dQ={final_dq}, "
                f"which is not less than TX_PDL_Q_ERROR={tx_pdl_q_error_val}. Results not saved to EEPROM.")

    def _set_rx_voa_control(self, enable: bool):
        """启用或禁用RX VOA控制环路。"""
        if enable:
            self.cim8.excecute_mcm_and_disp_result('set', 'VOA_RX_CNTL_ENABLE', [1, 1])
        else:
            self.cim8.handleDiagSetGetCmd('set', 'VOA_RX_CNTL_ENABLE', [1, 0])
            self.cim8.handleDiagSetGetCmd('set', 'BSP_DAC_REG', [1, 'PIC_RXVOA_XY', 0])

    def rx_tap_mon_cal_initial_setup(self) -> None:
        """RX TAP MON CAL的初始设置。"""
        self.cim8.waite_for_module_ready()
        self._configure_tx_freq_power(power=5)
        print('RX tap monitor校准开始')
        # 关闭rxvoa自动控制、VOA电压调到0
        self._set_rx_voa_control(enable=False)
        itla_power_dbm = self.cim8.excecute_mcm_and_disp_result('GET', 'ITLA_REG', [1, 0X42])[0] / 100
        lo_split_const = 0.25
        itla_power_mw_val = 10 ** (itla_power_dbm / 10) * lo_split_const
        return itla_power_mw_val

    def rx_tap_mon_cal_high_gain(self, rx_tap_monitor_range_list: List[float],
                                rx_tap_monitor_step_val: float, rx_pow_th_hilo_val: int) -> List[float]:
        """RX TAP MON CAL 高增益部分校准。

        Args:
            rx_tap_monitor_range_list: RX TAP监控范围列表
            rx_tap_monitor_step_val: RX TAP监控步长值
            rx_pow_th_hilo_val: RX功率阈值高低值

        Returns:
            校准结果列表
        """
        rx_pow_th_hilo_val = int(rx_pow_th_hilo_val)
        rx_tap_monitor_step_val = int(rx_tap_monitor_step_val)
        rxpd_threshold_val = self.cim8.excecute_mcm_and_disp_result('GET', 'PIC_RX_PWR_HG_THRESHOLD', [1])[0]
        rx_tap_monitor_list_high_val = [x for x in range(rx_tap_monitor_range_list[0], rx_pow_th_hilo_val + 1,
                                                         rx_tap_monitor_step_val)]
        rxpd_adc_high_list = []
        rx_tap_monitor_mw_high_list = []

        for rx_power_val in rx_tap_monitor_list_high_val:
            rx_read_val = self.set_rx_power(rx_power_val, 1)
            _, h_temp_val = self.cim8.excecute_mcm_and_disp_result("get", "BSP_ADC_REG", [1, 'PIC_RXPDXY_HIGH_GAIN'])
            rx_pd_adc_high_val = h_temp_val
            s_log = 'Rx_pwr(dBm),adc_high: %.2f %d' % (rx_read_val, rx_pd_adc_high_val)
            logger.info(s_log)
            if rx_pd_adc_high_val < rxpd_threshold_val:
                rx_tap_monitor_mw_high_list.append(10 ** (rx_read_val / 10))
                rxpd_adc_high_list.append(rx_pd_adc_high_val)
        # Check for empty lists to avoid error in linear_fit
        if not rx_tap_monitor_mw_high_list or not rxpd_adc_high_list:
            logger.warning("Warning: No data collected for high gain calibration. Slopes and offsets will be zero.")
            return 0.0, 0.0
        rx_pwr_hg_slope_val, rx_pwr_hg_offset_val = linear_fit(rx_tap_monitor_mw_high_list, rxpd_adc_high_list)
        return rx_pwr_hg_slope_val, rx_pwr_hg_offset_val

    def rx_tap_mon_cal_low_gain(self, rx_pow_th_hilo_val, rx_tap_monitor_step_val, itla_power_mw_val,
                                hispeed_pd_count_val):
        """RX TAP MON CAL 低增益部分校准"""
        rx_pow_th_hilo_val = int(rx_pow_th_hilo_val)
        rx_tap_monitor_step_val = int(rx_tap_monitor_step_val)
        rx_tap_monitor_list_low_val = [x for x in range(rx_pow_th_hilo_val, 0 + 1, rx_tap_monitor_step_val)]
        rxpd_adc_low_list = []
        rx_tap_monitor_mw_low_list = []
        rfpd_low_list = []

        for rx_power_val in rx_tap_monitor_list_low_val:
            rx_read_val = self.set_rx_power(rx_power_val, 1)
            _, l_temp_val = self.cim8.excecute_mcm_and_disp_result("get", "BSP_ADC_REG", [1, 'PIC_RXPDXY_LOW_GAIN'])
            rx_pd_adc_low_val = l_temp_val
            rfpd_ma_val, _ = self.cim8.excecute_mcm_and_disp_result("get", "BSP_ADC_REG", [1, 'CIM8_CUR_HSPD_LDO'])
            s_log = 'Rx_pwr(dBm),adc_low: %.2f %d' % (rx_read_val, rx_pd_adc_low_val)
            logger.info(s_log)
            rx_tap_monitor_mw_low_list.append(10 ** (rx_read_val / 10))
            rxpd_adc_low_list.append(rx_pd_adc_low_val)
            rfpd_low_list.append(rfpd_ma_val)

        if not rx_tap_monitor_mw_low_list or not rxpd_adc_low_list or not rfpd_low_list:  # Check for empty lists
            logger.warning("Warning: No data collected for low gain calibration. Slopes and offsets will be zero.")
            return 0.0, 0.0, 0.0, 0.0  # Return zero for all calculated values

        rx_pwr_lg_slope_val, rx_pwr_lg_offset_val = linear_fit(rx_tap_monitor_mw_low_list, rxpd_adc_low_list)
        rfpd_low_slope_val, rfpd_low_offset_val = linear_fit(rx_tap_monitor_mw_low_list, rfpd_low_list)

        rfpd_lo_resp_val = rfpd_low_offset_val / itla_power_mw_val if itla_power_mw_val != 0 else float('inf')
        rfpd_lo_resp_db_val = 10 * math.log10(
            rfpd_lo_resp_val / hispeed_pd_count_val) if rfpd_lo_resp_val > 0 and hispeed_pd_count_val > 0 else -float(
            'inf')
        rfpd_sig_resp_db_val = 10 * math.log10(
            rfpd_low_slope_val / hispeed_pd_count_val) if rfpd_low_slope_val > 0 and hispeed_pd_count_val > 0 else \
            -float('inf')
        return rx_pwr_lg_slope_val, rx_pwr_lg_offset_val, rfpd_lo_resp_db_val, rfpd_sig_resp_db_val

    def rx_tap_mon_cal_save_coeffs(self, rx_pwr_hg_slope, rx_pwr_hg_offset, rx_pwr_lg_slope, rx_pwr_lg_offset,
                                   rfpd_lo_resp_db, rfpd_sig_resp_db):
        """保存RX TAP MON CAL校准系数到EEPROM"""
        pic_temp_val = self.cim8.get_pic_temp()
        freq_val = self.cim8.get_freq()
        self.cim8.excecute_mcm_and_disp_result('ESET', 'PIC_RX_PWR_TEMP_REF', [1, pic_temp_val])
        self.cim8.excecute_mcm_and_disp_result('ESET', 'PIC_RXPD_RESP', [1, pic_temp_val, 0, freq_val])

        rx_power_high_slope_limit_const = [2e5, 8e8]
        rx_power_high_offset_limit_const = [1e2, 1.4e3]
        rx_power_low_slope_limit_const = [2000, 8000]
        rx_power_low_offset_limit_const = [-1e2, 1e2]

        if (rx_power_high_slope_limit_const[0] <= rx_pwr_hg_slope <= rx_power_high_slope_limit_const[1] and
                rx_power_high_offset_limit_const[0] <= rx_pwr_hg_offset <= rx_power_high_offset_limit_const[1] and
                rx_power_low_slope_limit_const[0] <= rx_pwr_lg_slope <= rx_power_low_slope_limit_const[1] and
                rx_power_low_offset_limit_const[0] <= rx_pwr_lg_offset <= rx_power_low_offset_limit_const[1]):
            self.cim8.excecute_mcm_and_disp_result('ESET', 'PIC_RX_PWR_HG_SLOPE_OFFSET',
                                                   [1, rx_pwr_hg_slope, rx_pwr_hg_offset])
            self.cim8.excecute_mcm_and_disp_result('ESET', 'PIC_RX_PWR_LG_SLOPE_OFFSET',
                                                   [1, rx_pwr_lg_slope, rx_pwr_lg_offset])
            self.cim8.excecute_mcm_and_disp_result('ESET', 'PIC_LO_RFPD_RESP_REF',
                                                   [1, pic_temp_val, freq_val, rfpd_lo_resp_db, rfpd_sig_resp_db])
        else:
            print("Warning: Calculated slopes/offsets are outside defined limits. Coefficients not saved to EEPROM.")

        self._set_rx_voa_control(enable=True)

    def rx_chn_pwr_cal_initial_setup(self) -> None:
        """RX Channel Power Calibration的初始设置。"""
        self.cim8.waite_for_module_ready()
        print('RX Channel Power Calibration 开始')
        # Update cal version to 1 before starting calibration
        self.cim8.handleDiagSetGetCmd("eset", "PIC_RX_CH_PWR_CAL_VER", [1, 1])
        self.cim8.handleDiagSetGetCmd("get", "PIC_CAL_DATA_LOAD", [1, 1])
        self._set_rx_voa_control(enable=False)  # 禁用Rx VOA环路控制并将驱动电流置零

        self.set_rx_power(-15, 5)
        self.cim8.handleDiagSetGetCmd("eset", "PIC_RXTIA_SETTINGS", [1, 1])  # save tia init values

        gc_mon_ave_adc_max_volts_const = 2.7
        gc_mon_ave_adc_max_counts_ref_const = 62000
        rx_ch_mon_fine_thrld_const = 2.2

        slope, offset = self.cim8.excecute_mcm_and_disp_result('EGET', 'BSP_ADC_EEPROM_SCALING', [1, 'PIC_TIAGC1X_MON'])
        gc_mon_ave_adc_max_counts_calc = (gc_mon_ave_adc_max_volts_const - offset) / slope if slope != 0 else float(
            'inf')
        gc_mon_ave_adc_max_counts = min(gc_mon_ave_adc_max_counts_ref_const, gc_mon_ave_adc_max_counts_calc)
        rx_ch_mon_fine_thrld_counts = (rx_ch_mon_fine_thrld_const - offset) / slope if slope != 0 else float('inf')
        return gc_mon_ave_adc_max_counts, rx_ch_mon_fine_thrld_counts

    def _ave_qef_mon(self):
        """获取平均QEF Monitor值"""
        dataX = self.cim8.excecute_mcm_and_disp_result('GET', 'ASIC_REG', [1, 0xF0104010])[0]
        QEFXI = dataX & 0x3FFF
        QEFXQ = (dataX >> 16) & 0x3FFF
        dataY = self.cim8.excecute_mcm_and_disp_result('GET', 'ASIC_REG', [1, 0xF0104014])[0]
        QEFYI = dataY & 0x3FFF
        QEFYQ = (dataY >> 16) & 0x3FFF
        logger.info(f"dataX:{dataX}, QEFXI:{QEFXI}, QEFXQ:{QEFXQ}")
        logger.info(f"dataY:{dataY}, QEFYI:{QEFYI}, QEFYQ:{QEFYQ}")
        return np.mean([QEFXI, QEFXQ, QEFYI, QEFYQ])

    def _picp_avg_gc_mon(self):
        """获取平均PICP GC Monitor值"""
        TIAs = []
        datas = ['PIC_TIAGC1X_MON', 'PIC_TIAGC1Y_MON', 'PIC_TIAGC2X_MON', 'PIC_TIAGC2Y_MON']
        for i in datas:
            adc = self.cim8.excecute_mcm_and_disp_result("get", "BSP_ADC_REG", [1, i])[1]
            TIAs.append(adc)
        return sum(TIAs) / 4 if TIAs else 0, TIAs

    def rx_chn_pwr_cal_collect_data(self, rx_ch_mon_start_val, rx_ch_mon_coarse_step_val, rx_ch_mon_fine_step_val,
                                    gc_mon_ave_adc_max_counts_val, rx_ch_mon_fine_thrld_counts_val,
                                    qef_threshold_val, gc_mon_slope_max_val):
        """收集RX Channel Power校准数据"""

        rx_ch_mon_fine_step_val = float(rx_ch_mon_fine_step_val)
        qef_threshold_val = float(qef_threshold_val)
        gc_mon_slope_max_val = int(gc_mon_slope_max_val)
        self.set_rx_power(-6, 5)
        gc_mon_ave_qef_ref = self._ave_qef_mon()
        self.cim8.handleDiagSetGetCmd('eset', 'PIC_RX_CH_QEF_REF', [1, float2hex(gc_mon_ave_qef_ref)])

        rx_channel_monitor_list = []
        gc_mon_ave_adc_list = []

        inpwr_val = self.set_rx_power(rx_ch_mon_start_val, osw_tr_sleep=0)
        ave_adc_val, _ = self._picp_avg_gc_mon()
        rx_channel_monitor_list.append(inpwr_val)
        gc_mon_ave_adc_list.append(ave_adc_val)

        current_rx_ch_mon_step = rx_ch_mon_coarse_step_val
        gc_mon_ave_qef_limit = gc_mon_ave_qef_ref * 10 ** (-0.1 * qef_threshold_val)
        fine_step_flag_val = 0
        current_inpwr = rx_ch_mon_start_val

        for index in range(1, 100):
            current_inpwr -= current_rx_ch_mon_step
            inpwr_act_val = self.set_rx_power(current_inpwr, osw_tr_sleep=1)
            gc_mon_adc_ave_val, tias_val = self._picp_avg_gc_mon()
            current_gc_mon_ave_qef = self._ave_qef_mon()
            saturated_num_val = sum(1 for tia in tias_val if tia >= gc_mon_ave_adc_max_counts_val)

            if saturated_num_val >= 3 or current_gc_mon_ave_qef <= gc_mon_ave_qef_limit:
                break

            if gc_mon_adc_ave_val < rx_ch_mon_fine_thrld_counts_val:
                current_rx_ch_mon_step = rx_ch_mon_coarse_step_val
                fine_step_flag_val = 0
            elif gc_mon_adc_ave_val >= rx_ch_mon_fine_thrld_counts_val:
                current_rx_ch_mon_step = rx_ch_mon_fine_step_val
                fine_step_flag_val = 1

            rx_channel_monitor_list.append(inpwr_act_val)
            gc_mon_ave_adc_list.append(gc_mon_adc_ave_val)

            if index > 0:  # Ensure there's a previous point to calculate slope
                dGC_MON = gc_mon_ave_adc_list[index] - gc_mon_ave_adc_list[index - 1]
                dPwr = rx_channel_monitor_list[index] - rx_channel_monitor_list[index - 1]
                gc_mon_slope = dGC_MON / dPwr if dPwr != 0 else float('inf')
                logger.info(
                    'INPOWER:%s dBm; GC_MON_AVE_ADC:%d; SLOPE:%.2f' % (inpwr_act_val, gc_mon_adc_ave_val, gc_mon_slope))
                if len(gc_mon_ave_adc_list) > 39 or \
                        (gc_mon_slope >= gc_mon_slope_max_val and fine_step_flag_val == 1) or \
                        gc_mon_adc_ave_val >= gc_mon_ave_adc_max_counts_val:
                    break
            else:
                logger.warning('INPOWER:%s dBm; GC_MON_AVE_ADC:%d' % (inpwr_act_val, gc_mon_adc_ave_val))

        logger.info(rx_channel_monitor_list)
        logger.info(gc_mon_ave_adc_list)
        return rx_channel_monitor_list, gc_mon_ave_adc_list

    def rx_chn_pwr_cal_save_results(self, rx_channel_monitor: List[float],
                                   gc_mon_ave_adc: List[int], gc_mon_ave_adc_max_counts_ref: int) -> None:
        """保存RX Channel Power校准结果到EEPROM。

        Args:
            rx_channel_monitor: RX通道监控值列表
            gc_mon_ave_adc: GC监控平均ADC值列表
            gc_mon_ave_adc_max_counts_ref: GC监控平均ADC最大计数参考值
        """
        for i in range(40):
            self.cim8.excecute_mcm_and_disp_result("eset", "PIC_RX_CH_PWR_CAL",
                                                   [1, i, float2hex(float('nan')), gc_mon_ave_adc_max_counts_ref])
        for i in range(len(rx_channel_monitor)):
            self.cim8.excecute_mcm_and_disp_result("eset", "PIC_RX_CH_PWR_CAL",
                                                   [1, i, float(rx_channel_monitor[i]), int(gc_mon_ave_adc[i])])
        self.cim8.excecute_mcm_and_disp_result("eset", "PIC_EEPROM_HDR", [1, 0xCAFEBABE])
        pic_temp_ref = self.cim8.get_pic_temp()
        baud_rate_ref = self.cim8.get_baud_rate()
        self.cim8.excecute_mcm_and_disp_result("eset", "PIC_RX_CH_TEMP_REF", [1, float2hex(pic_temp_ref)])
        self.cim8.excecute_mcm_and_disp_result("eset", "PIC_RX_CH_BAUD_RATE_REF", [1, float2hex(baud_rate_ref)])
        self.cim8.excecute_mcm_and_disp_result("eset", "PIC_RX_CH_PWR_CAL_VER", [1, 1])
        self._set_rx_voa_control(enable=True)
        self.cim8.excecute_mcm_and_disp_result('eset', 'PIC_RX_CH_GC_TEMP_BD0',
                                               [1, 1100.00000, 1050.00000, 1050.00000, 850.00000, 650.00000])
        self.cim8.excecute_mcm_and_disp_result('eset', 'PIC_RX_CH_GC_TEMP_BD1',
                                               [1, 950.00000, 700.00000, 650.00000, 250.00000, -1.000000e+02])
        self.cim8.excecute_mcm_and_disp_result('eset', 'PIC_RX_CH_GC_TEMP_BD2',
                                               [1, 400.00000, 150.00000, -2.000000e+02, -4.500000e+02, -1.000000e+03])
        self.cim8.excecute_mcm_and_disp_result('eset', 'PIC_RX_CH_GC_TEMP_BD_REFS',
                                               [1, -6.000000e+01, -2.000000e+01, 20.00000])
        self.cim8.excecute_mcm_and_disp_result('eset', 'PIC_RX_CH_GC_TEMP_REFS',
                                               [1, -3.000000e+01, -1.500000e+01, 0.00000, 15.00000, 30.00000])
        self.reset_module_and_wait()
        logger.info('RX Channel Power Calibration 结束\n\n')

    def _set_mdl_opt_control(self, enable: bool) -> None:
        """启用或禁用MDL OPT控制环路。

        Args:
            enable: True启用控制环路，False禁用控制环路。
        """
        val_switch = 1 if enable else 0
        self.cim8.excecute_mcm_and_disp_result('set', 'PIC_SW_CNTL_MDL_OPT', [1, val_switch])

    def rx_pdl_mon_cal_get_stable_ratio(self) -> Tuple[Optional[float], bool]:
        """循环获取稳定的PDL比率。

        通过多次重置和读取来获取稳定的PDL比率值。

        Returns:
            (PDL比率值, 是否稳定)的元组。
        """
        max_resets = 10
        ratios_list = []  # 存储返回前的最后一组读数

        for _ in range(max_resets):
            self._set_mdl_opt_control(enable=False)  # 禁用MDL控制环路
            self.set_rx_power(-5, osw_tr_sleep=0)  # 休眠/等待由后续的wait_for_trx_turnup处理
            self.cim8.wait_for_trx_turnup()

            current_ratios = []
            read_attempts = 10  # 读十次取平均
            for _ in range(read_attempts):
                # 假设元组的第一个元素是RXPDL_RatiowoDiv
                rxdl_ratiowo_div, _, _ = self.cim8.excecute_mcm_and_disp_result('GET', 'PIC_RX_OPT_PWR_RATIO', [1])
                current_ratios.append(rxdl_ratiowo_div)
                time.sleep(0.1)

            ratios_list = current_ratios  # 存储此次尝试的读数

            if not current_ratios:  # 处理未读取到比率的情况
                logger.warning('***此次尝试无法读取PDL比率，正在重置。')
                self.reset_module_and_wait(sleep_duration=0, ensure_ready_after_normal_power=True)
                continue

            final_rxdl_ratio = np.mean(current_ratios)
            # 检查current_ratios中的所有元素是否相同
            if len(set(current_ratios)) == 1 and len(current_ratios) > 0:  # 所有元素相同
                logger.warning('***PDL比率没有更新，重置')
                self.reset_module_and_wait(sleep_duration=0)
            else:  # 比率不同，意味着可能已更新/稳定
                logger.info(f"PDL比率已稳定: {final_rxdl_ratio}")
                return final_rxdl_ratio, True  # 找到并稳定的比率

        logger.error("***多次重置后仍无法获得稳定的PDL比率。")
        # 即使不稳定也返回最后已知的比率列表，用于原始逻辑的最终检查
        return np.mean(ratios_list) if ratios_list else None, False

    def rx_pdl_mon_cal_save_and_cleanup(self, rxdl_ratio_val: float, was_stable: bool) -> None:
        """保存校准结果并清理。

        Args:
            rxdl_ratio_val: RXDL比率值
            was_stable: 是否稳定
        """
        # The original logic for saving was `if max(ratio_l) != min(ratio_l):`
        # This is equivalent to `was_stable` being True and rxdl_ratio_val being not None
        if was_stable and rxdl_ratio_val is not None:
            self.cim8.excecute_mcm_and_disp_result('ESET', 'PIC_RX_PDL_MON_CAL', [1, float2hex(rxdl_ratio_val)])
            self.cim8.excecute_mcm_and_disp_result('ESET', 'PIC_EEPROM_HDR', [1, 0xCAFEBABE])
        else:
            logger.info("***PDL Ratio was not stable or not found. Not saving to EEPROM.")

        self._set_mdl_opt_control(enable=True)  # enable MDL control loop
        self.cim8.SoftmodRst()
        logger.info('RX PDL MONITOR校准结束\n\n')
